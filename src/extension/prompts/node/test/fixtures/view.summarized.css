.monaco-editor {
	.inline-edit.suggest-preview-text .ghost-text {
		&.syntax-highlighted {…}
		background: var(--vscode-inlineEdit-modifiedChangedTextBackground) !important;
		outline: 2px slid var(--vscode-inlineEdit-modifiedChangedTextBackground) !important;

		font-style: normal !important;
	}
}

.monaco-menu-option {
	color: var(--vscode-editorActionList-foreground);
	font-size: 13px;
	padding: 0 10px;
	line-height: 26px;
	display: flex;
}
