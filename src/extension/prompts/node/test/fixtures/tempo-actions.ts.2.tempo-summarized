
export class MenuId {
	static readonly InlineEditActions = new MenuId('InlineEditActions');
	static readonly NewFile = new MenuId('NewFile');
	static readonly MergeInput1Toolbar = new MenuId('MergeToolbar1Toolbar');
	static readonly MergeInput2Toolbar = new MenuId('MergeToolbar2Toolbar');
	static readonly MergeBaseToolbar = new MenuId('MergeBaseToolbar');
	static readonly MergeInputResultToolbar = new MenuId('MergeToolbarResultToolbar');
	static readonly InlineSuggestionToolbar = new MenuId('InlineSuggestionToolbar');
	static readonly InlineEditToolbar = new MenuId('InlineEditToolbar');
	static readonly ChatContext = new MenuId('ChatContext');
	static readonly ChatCodeBlock = new MenuId('ChatCodeblock');
	static readonly ChatCompareBlock = new MenuId('ChatCompareBlock');
	static readonly ChatMessageTitle = new MenuId('ChatMessageTitle');
	static readonly ChatMessageFooter = new MenuId('ChatMessageFooter');
	static readonly ChatExecute = new MenuId('ChatExecute');
	static readonly ChatExecuteSecondary = new MenuId('ChatExecuteSecondary');
	static readonly ChatInput = new MenuId('ChatInput');
	static readonly ChatInputSide = new MenuId('ChatInputSide');
	static readonly ChatInlineResourceAnchorContext = new MenuId('ChatInlineResourceAnchorContext');
	static readonly ChatInlineSymbolAnchorContext = new MenuId('ChatInlineSymbolAnchorContext');
	static readonly ChatCommandCenter = new MenuId('ChatCommandCenter');
	static readonly AccessibleView = new MenuId('AccessibleView');
	static readonly MultiDiffEditorFileToolbar = new MenuId('MultiDiffEditorFileToolbar');
	static readonly DiffEditorHunkToolbar = new MenuId('DiffEditorHunkToolbar');
	static readonly DiffEditorSelectionToolbar = new MenuId('DiffEditorSelectionToolbar');


	/**
	 * Create or reuse a `MenuId` with the given identifier
	 */
	static for(identifier: string): MenuId {
		return MenuId._instances.get(identifier) ?? new MenuId(identifier);
	}

	readonly id: string;

	/**
	 * Create a new `MenuId` with the unique identifier. Will throw if a menu
	 * with the identifier already exists, use `MenuId.for(ident)` or a unique
	 * identifier
	 */
	constructor(identifier: string) {
		if (MenuId._instances.has(identifier)) {
			throw new TypeError(`MenuId with identifier '${identifier}' already exists. Use MenuId.for(ident) or a unique identifier`);
		}
		MenuId._instances.set(identifier, this);
		this.id = identifier;
	}
}

export interface IMenuActionOptions {
	arg?: any;
	shouldForwardArgs?: boolean;
	renderShortTitle?: boolean;
}

export interface IMenuChangeEvent {
	readonly menu: IMenu;
	readonly isStructuralChange: boolean;
	readonly isToggleChange: boolean;
	readonly isEnablementChange: boolean;
}

