/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/*
	@keyframes blink { 50% { border-color: orange; }  }
*/

.monaco-editor {
	.inline-edits-view-indicator {
		display: flex;

		z-index: 34; /* Below the find widget */
		height: 20px;

		color: var(--vscode-inlineEdit-indicator-foreground);
		background-color: var(--vscode-inlineEdit-indicator-background);
		border: 1px solid var(--vscode-inlineEdit-indicator-border);
		border-radius: 3px;

		align-items: center;
		padding: 2px;
		padding-right: 10px;
		margin: 0 4px;

		/*
		animation: blink 1s;
		animation-iteration-count: 3;
		*/

		opacity: 0;

		&.contained {
			transition: opacity 0.2s ease-in-out;
			transition-delay: 0.4s;
		}

		&.visible {
			opacity: 1;
		}

		&.top {
			opacity: 1;

			.icon {
				transform: rotate(90deg);
			}
		}

		&.bottom {
			opacity: 1;

			.icon {
				transform: rotate(-90deg);
			}
		}

		.icon {
			display: flex;
			align-items: center;
			margin: 0 2px;
			transform: none;
			transition: transform 0.2s ease-in-out;
			.codicon {
				color: var(--vscode-inlineEdit-indicator-foreground);
			}
		}

		.label {
			margin: 0 2px;

			display: flex;
			justify-content: center;
			width: 100%;
		}
	}

	.inline-edits-view {
		&.toolbarDropdownVisible, .editorContainer.showHover:hover {
			.toolbar {
				display: block;
			}
		}

		.editorContainer {
			color: var(--vscode-editorHoverWidget-foreground);

			.toolbar {
				display: none;
				border-top: 1px solid rgba(69, 69, 69, 0.5);
				background-color: var(--vscode-editorHoverWidget-statusBarBackground);

				a {
					color: var(--vscode-foreground);
				}

				a:hover {
					color: var(--vscode-foreground);
				}

				.keybinding {
					display: flex;
					margin-left: 4px;
					opacity: 0.6;
				}

				.keybinding .monaco-keybinding-key {
					font-size: 8px;
					padding: 2px 3px;
				}

				.availableSuggestionCount a {
					display: flex;
					min-width: 19px;
					justify-content: center;
				}

				.inlineSuggestionStatusBarItemLabel {
					margin-right: 2px;
				}

			}

			.preview {
				.monaco-editor {
					.view-overlays .current-line-exact {
						border: none;
					}

					.current-line-margin {
						border: none;
					}
				}
			}

			.inline-edits-view-zone.diagonal-fill {
				opacity: 0.5;
			}
		}
	}

	.strike-through {
		text-decoration: line-through;
	}

	.inlineCompletions-line-insert {
		background: var(--vscode-inlineEdit-modifiedChangedLineBackground);
	}

	.inlineCompletions-line-delete {
		background: var(--vscode-inlineEdit-originalChangedLineBackground);
	}

	.inlineCompletions-char-insert {
		background: var(--vscode-inlineEdit-modifiedChangedTextBackground);
	}

	.inlineCompletions-char-delete {
		background: var(--vscode-inlineEdit-originalChangedTextBackground);
	}

	.inlineCompletions-char-delete.diff-range-empty {
		margin-left: -1px;
		border-left: solid var(--vscode-inlineEdit-originalChangedTextBackground) 3px;
	}

	.inlineCompletions-char-insert.diff-range-empty {
		border-left: solid var(--vscode-inlineEdit-modifiedChangedTextBackground) 3px;
	}

	.inlineCompletions-char-delete.single-line-inline,
	.inlineCompletions-char-insert.single-line-inline {
		border-radius: 4px;
		border: 1px solid var(--vscode-editorHoverWidget-border);
		padding: 2px;
	}

	.inlineCompletions-char-delete.single-line-inline.empty,
	.inlineCompletions-char-insert.single-line-inline.empty {
		display: none;
	}

	/* Adjustments due to being a decoration */
	.inlineCompletions-char-delete.single-line-inline {
		margin: -2px 0 0 -2px;
	}

	.inlineCompletions.strike-through {
		text-decoration-thickness: 1px;
	}

	/* line replacement bubbles */

	.inlineCompletions-modified-bubble{
		background: var(--vscode-inlineEdit-modifiedChangedTextBackground);
	}

	.inlineCompletions-original-bubble{
		background: var(--vscode-inlineEdit-originalChangedTextBackground);
		border-radius: 4px;
	}

	.inlineCompletions-modified-bubble,
	.inlineCompletions-original-bubble {
		pointer-events: none;
	}

	.inlineCompletions-modified-bubble.start {
		border-top-left-radius: 4px;
		border-bottom-left-radius: 4px;
	}

	.inlineCompletions-modified-bubble.end {
		border-top-right-radius: 4px;
		border-bottom-right-radius: 4px;
	}

	.inline-edit.ghost-text,
	.inline-edit.ghost-text-decoration,
	.inline-edit.ghost-text-decoration-preview,
	.inline-edit.suggest-preview-text .ghost-text {
		&.syntax-highlighted {
			opacity: 1 !important;
		}
		background: var(--vscode-inlineEdit-modifiedChangedTextBackground) !important;
		outline: 2px slid var(--vscode-inlineEdit-modifiedChangedTextBackground) !important;

		font-style: normal !important;
	}
}

.monaco-menu-option {
	color: var(--vscode-editorActionList-foreground);
	font-size: 13px;
	padding: 0 10px;
	line-height: 26px;
	display: flex;
	gap: 8px;
	align-items: center;
	border-radius: 4px;
	cursor: pointer;

	&.active {
		background: var(--vscode-editorActionList-focusBackground);
		color: var(--vscode-editorActionList-focusForeground);
		outline: 1px solid var(--vscode-menu-selectionBorder, transparent);
		outline-offset: -1px;
	}
}

.inline-edits-view-gutter-indicator .codicon {
	margin-top: 1px; /* TODO: Move into gutter DOM initialization */
}

@keyframes wiggle {
	0% {
		transform: rotate(0) scale(1);
	}

	15%,
	45% {
		transform: rotate(.04turn) scale(1.1);
	}

	30%,
	60% {
		transform: rotate(-.04turn) scale(1.2);
	}

	100% {
		transform: rotate(0) scale(1);
	}
}

.inline-edits-view-gutter-indicator.wiggle .icon {
	animation-duration: .8s;
	animation-iteration-count: 1;
	animation-name: wiggle;
}
