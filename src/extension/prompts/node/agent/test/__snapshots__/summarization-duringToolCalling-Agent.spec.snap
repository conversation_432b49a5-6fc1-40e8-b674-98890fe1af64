### User
~~~md
When generating code, please follow these user provided coding instructions. You can ignore an instruction if it contradicts a system message.
<instructions>
This is a test custom instruction file
</instructions>
~~~


### User
~~~md
<environment_info>
The user's current OS is: Linux
The user's default shell is: "zsh". When you generate terminal commands, please generate them correctly for this shell.
</environment_info>
<workspace_info>
I am working in a workspace with the following folders:
- /workspace 
I am working in a workspace that has the following structure:
```

```
This is the state of the context at this point in the conversation. The view of the workspace structure may be truncated. You can use tools to collect more context if needed.
</workspace_info>
copilot_cache_control: {"type":"ephemeral"}
~~~


### User
~~~md
<conversation-summary>
summarized!
</conversation-summary>
copilot_cache_control: {"type":"ephemeral"}
~~~


### Assistant
~~~md
ok 3
🛠️ insert_edit_into_file (tooluse_3) {
  "filePath": "/workspace/file.ts",
  "code": "// existing code...
console.log('hi')"
}
~~~


### Tool
~~~md
🛠️ tooluse_3
success
copilot_cache_control: {"type":"ephemeral"}
~~~
