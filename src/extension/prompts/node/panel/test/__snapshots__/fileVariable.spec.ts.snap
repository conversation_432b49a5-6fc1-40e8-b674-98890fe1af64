// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`FileVariable > does include known untitled file 1`] = `
"{
  "node": {
    "type": 1,
    "ctor": 2,
    "ctorName": "FileVariable",
    "children": [
      {
        "type": 1,
        "ctor": 2,
        "ctorName": "CodeSummary",
        "children": [
          {
            "type": 1,
            "ctor": 2,
            "ctorName": "Tag",
            "children": [
              {
                "type": 1,
                "ctor": 2,
                "ctorName": "KeepWith",
                "children": [
                  {
                    "type": 2,
                    "priority": 9007199254740991,
                    "text": "<attachment>\\n",
                    "lineBreakBefore": false
                  }
                ],
                "props": {},
                "references": [],
                "keepWithId": 0
              },
              {
                "type": 1,
                "ctor": 2,
                "ctorName": "TagInner",
                "children": [
                  {
                    "type": 2,
                    "priority": 1,
                    "text": "",
                    "lineBreakBefore": false
                  },
                  {
                    "type": 1,
                    "ctor": 2,
                    "ctorName": "CodeBlock",
                    "children": [
                      {
                        "type": 1,
                        "ctor": 2,
                        "ctorName": "TextChunk",
                        "children": [
                          {
                            "type": 2,
                            "priority": 9007199254740991,
                            "text": "\`\`\`python\\ntest!\\n\`\`\`",
                            "references": [
                              {
                                "anchor": {
                                  "$mid": 1,
                                  "external": "untitled:Untitled-1",
                                  "path": "Untitled-1",
                                  "scheme": "untitled"
                                }
                              }
                            ],
                            "lineBreakBefore": true
                          }
                        ],
                        "props": {},
                        "references": []
                      }
                    ],
                    "props": {},
                    "references": []
                  },
                  {
                    "type": 2,
                    "priority": 1,
                    "text": "\\n",
                    "lineBreakBefore": false
                  }
                ],
                "props": {
                  "flexGrow": 1,
                  "priority": 1
                },
                "references": []
              },
              {
                "type": 1,
                "ctor": 2,
                "ctorName": "KeepWith",
                "children": [
                  {
                    "type": 2,
                    "priority": 9007199254740991,
                    "text": "</attachment>",
                    "lineBreakBefore": false
                  }
                ],
                "props": {},
                "references": [],
                "keepWithId": 0
              },
              {
                "type": 2,
                "priority": 9007199254740991,
                "text": "\\n",
                "lineBreakBefore": false
              }
            ],
            "props": {},
            "references": []
          }
        ],
        "props": {},
        "references": []
      }
    ],
    "props": {},
    "references": []
  }
}"
`;

exports[`FileVariable > does not include unknown untitled file 1`] = `
{
  "node": {
    "children": [],
    "ctor": 2,
    "ctorName": "FileVariable",
    "props": {},
    "references": [],
    "type": 1,
  },
}
`;
