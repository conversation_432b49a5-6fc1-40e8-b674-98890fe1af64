#%% vscode.cell [id=ba5f469c] [language=markdown]
"""
# Sample Notebook
"""
#%% vscode.cell [id=eb4326f0] [language=markdown]
"""
## First cell contains imports and second cell some helper functions
"""
#%% vscode.cell [id=f69a1e51] [language=python]
import sys
import os
#%% vscode.cell [id=8af52d61] [language=python]
def do_something():
    print("Hello from Python!")
    print("The current working directory is: " + os.getcwd())
#%% vscode.cell [id=79849545] [language=markdown]
"""
## Print hello world
"""
#%% vscode.cell [id=176546a8] [language=python]
print("Hello World")
print(sys.executable)
#%% vscode.cell [id=603ae9f2] [language=markdown]
"""
## Cell with trailing empty lines
"""
#%% vscode.cell [id=3b183e93] [language=python]
print("Python version")






#%% vscode.cell [id=964a95a5] [language=python]