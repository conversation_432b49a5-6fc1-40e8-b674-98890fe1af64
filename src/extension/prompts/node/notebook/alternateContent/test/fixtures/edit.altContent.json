{"cells": [{"cell_type": "code", "id": "CELL_ID_0", "metadata": {"language": "python"}, "source": ["import numpy as np", "import pandas as pd", "import matplotlib.pyplot as plt", "", "p = np.linspace(0, 20, 100)", "plt.plot(p, np.sin(15 * p))", "plt.show()", ""]}, {"cell_type": "code", "id": "CELL_ID_1", "metadata": {"language": "python"}, "source": ["# Create an example dataframe about a fictional army", "raw_data = {'regiment': ['Nighthawks', 'Nighthawks', 'Nighthawks', 'Nighthawks', 'Dragoons', 'Dragoons', 'Dragoons', 'Dragoons', 'Scouts', 'Scouts', 'Scouts', 'Scouts'],", "            'company': ['1st', '1st', '2nd', '2nd', '1st', '1st', '2nd', '2nd','1st', '1st', '2nd', '2nd'],", "            'deaths': [523, 52, 25, 616, 43, 234, 523, 62, 62, 73, 37, 35],", "            'battles': [5, 42, 2, 2, 4, 7, 8, 3, 4, 7, 8, 9],", "            'size': [1045, 957, 1099, 1400, 1592, 1006, 987, 849, 973, 1005, 1099, 1523],", "            'veterans': [1, 5, 62, 26, 73, 37, 949, 48, 48, 435, 63, 345],", "            'readiness': [1, 2, 3, 3, 2, 1, 2, 3, 2, 1, 2, 3],", "            'armored': [1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1],", "            'deserters': [4, 24, 31, 2, 3, 4, 24, 31, 2, 3, 2, 3],", "            'origin': ['Arizona', 'California', 'Texas', 'Florida', 'Maine', 'Iowa', 'Alaska', 'Washington', 'Oregon', 'Wyoming', 'Louisana', 'Georgia']}", "", "army = pd.DataFrame(data=raw_data)", "army.set_index('origin', inplace=True)", ""]}, {"cell_type": "code", "id": "CELL_ID_2", "metadata": {"language": "python"}, "source": ["raw_data = {'regiment': ['Nighthawks', 'Nighthawks', 'Nighthawks', 'Nighthawks', 'Dragoons', 'Dragoons', 'Dragoons', 'Dragoons', 'Scouts', 'Scouts', 'Scouts', 'Scouts'],", "        'company': ['1st', '1st', '2nd', '2nd', '1st', '1st', '2nd', '2nd','1st', '1st', '2nd', '2nd'],", "        'name': ['<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],", "        'preTestScore': [4, 24, 31, 2, 3, 4, 24, 31, 2, 3, 2, 3],", "        'postTestScore': [25, 94, 57, 62, 70, 25, 94, 57, 62, 70, 62, 70]}", "regiment = pd.DataFrame(raw_data, columns = raw_data.keys())", ""]}], "metadata": {"kernelspec": {"language": "python"}, "language_info": {"name": "python"}}}