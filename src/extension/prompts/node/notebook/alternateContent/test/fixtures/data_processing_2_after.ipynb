{"cells": [{"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["# 1. <PERSON>up\n", "We'll create a simple dataset simulating sensor readings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "raw_data = {\n", "    'time': [\n", "        '2025-01-01 09:00:00',\n", "        '2025-01-01 09:15:00',\n", "        '2025-01-01 09:30:00',\n", "        '2025-01-02 10:00:00'\n", "    ],\n", "    'sensor_value': [10.5, 10.7, 10.3, 12.1]\n", "}\n", ""]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["# Prep"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def preprocess_data(data: dict) -> pd.DataFrame:\n", "    df = pd.DataFrame(data)\n", "    df['time'] = pd.to_datetime(df['time'])  # Convert to datetime\n", "    df['sensor_value'] = df['sensor_value'] / df['sensor_value'].max()\n", "    return df"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df_prepped = preprocess_data(raw_data)\n", "df_prepped['date_part'] = df_prepped['time'].dt.date  # Use datetime accessor\n", "print(\"Dates (datetime):\", df_prepped['date_part'].tolist())"]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["# Aggregation Attempt\n", "We do a naive grouping by `date_part` (string-based) to compute average sensor readings."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["daily_avg = df_prepped.groupby('date_part', as_index=True)['sensor_value'].mean()\n", "print(\"Daily Average:\")\n", "print(daily_avg.to_frame())"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}