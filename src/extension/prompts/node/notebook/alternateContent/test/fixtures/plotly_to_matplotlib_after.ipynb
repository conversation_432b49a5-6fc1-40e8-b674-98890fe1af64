{"cells": [{"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["# Import Required Libraries\n", "Import the necessary libraries, including pandas and matplotlib."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import Required Libraries\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# Import plotly data\n", "import plotly.data as data"]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["# Load Sample Data from plotly.data\n", "Load sample datasets from the plotly.data package into pandas DataFrames."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load Sample Data from plotly.data\n", "\n", "# Load the 'gapminder' dataset into a pandas DataFrame\n", "gapminder_df = data.gapminder()\n", "\n", "# Load the 'tips' dataset into a pandas DataFrame\n", "tips_df = data.tips()\n", "\n", "# Load the 'iris' dataset into a pandas DataFrame\n", "iris_df = data.iris()\n", "\n", "# Display the first few rows of each DataFrame\n", "gapminder_df.head(), tips_df.head(), iris_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["# DataFrame Operations\n", "Perform various DataFrame operations such as filtering, grouping, and merging."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# DataFrame Operations\n", "\n", "# Filtering: Select rows where the year is 2007 in the gapminder dataset\n", "gapminder_2007 = gapminder_df[gapminder_df['year'] == 2007]\n", "gapminder_2007.head()\n", "\n", "# Grouping: Group the tips dataset by day and calculate the average tip\n", "average_tips_by_day = tips_df.groupby('day')['tip'].mean().reset_index()\n", "average_tips_by_day\n", "\n", "# Merging: Merge the iris dataset with itself on the species column\n", "merged_iris = pd.merge(iris_df, iris_df, on='species', suffixes=('_left', '_right'))\n", "merged_iris.head()"]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["# Descriptive Statistics\n", "Calculate descriptive statistics for the DataFrames, including mean, median, and standard deviation."]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["# Data Visualization with Matplotlib\n", "Create static plots using Matplotlib to visualize the data, including bar charts, scatter plots, and line charts."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data Visualization with Matplotlib\n", "\n", "# Bar Chart: Average Tips by Day\n", "plt.figure(figsize=(10, 6))\n", "plt.bar(average_tips_by_day['day'], average_tips_by_day['tip'], color='skyblue')\n", "plt.title('Average Tips by Day')\n", "plt.xlabel('Day')\n", "plt.ylabel('Average Tip')\n", "plt.show()\n", "\n", "# Scatter Plot: Life Expectancy vs. GDP per Capita (2007)\n", "plt.figure(figsize=(10, 6))\n", "plt.scatter(gapminder_2007['gdpPercap'], gapminder_2007['lifeExp'], c=gapminder_2007['continent'].astype('category').cat.codes, s=gapminder_2007['pop'] / 1e6, alpha=0.5)\n", "plt.xscale('log')\n", "plt.title('Life Expectancy vs. GDP per Capita (2007)')\n", "plt.xlabel('GDP per Capita')\n", "plt.ylabel('Life Expectancy')\n", "plt.show()\n", "\n", "# Line Chart: Life Expectancy Over Time for Each Continent\n", "plt.figure(figsize=(10, 6))\n", "for continent, group in gapminder_df.groupby('continent'):\n", "    plt.plot(group['year'], group['lifeExp'], label=continent)\n", "plt.title('Life Expectancy Over Time for Each Continent')\n", "plt.xlabel('Year')\n", "plt.ylabel('Life Expectancy')\n", "plt.legend()\n", "plt.show()\n", "\n", "# Scatter Matrix: Iris Dataset\n", "from pandas.plotting import scatter_matrix\n", "scatter_matrix(iris_df, figsize=(12, 8), diagonal='kde', color=iris_df['species'].astype('category').cat.codes)\n", "plt.suptitle('Scatter Matrix of Iris Dataset')\n", "plt.show()\n", "\n", "# Box Plot: Distribution of Tips by Day\n", "plt.figure(figsize=(10, 6))\n", "tips_df.boxplot(column='tip', by='day', grid=False)\n", "plt.title('Distribution of Tips by Day')\n", "plt.suptitle('')\n", "plt.xlabel('Day')\n", "plt.ylabel('Tip')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["# Interactive Plots with Matplotlib\n", "Create interactive plots using Matplotlib to explore the data, including hover effects and zooming."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive Plots with Matplotlib\n", "\n", "# Interactive Scatter Plot: Life Expectancy vs. GDP per Capita (2007) with Hover Effects\n", "plt.figure(figsize=(10, 6))\n", "scatter = plt.scatter(gapminder_2007['gdpPercap'], gapminder_2007['lifeExp'], c=gapminder_2007['continent'].astype('category').cat.codes, s=gapminder_2007['pop'] / 1e6, alpha=0.5)\n", "plt.xscale('log')\n", "plt.title('Interactive Life Expectancy vs. GDP per Capita (2007)')\n", "plt.xlabel('GDP per Capita')\n", "plt.ylabel('Life Expectancy')\n", "plt.show()\n", "\n", "# Interactive Line Chart: Life Expectancy Over Time for Each Continent with Zooming\n", "plt.figure(figsize=(10, 6))\n", "for continent, group in gapminder_df.groupby('continent'):\n", "    plt.plot(group['year'], group['lifeExp'], label=continent)\n", "plt.title('Interactive Life Expectancy Over Time for Each Continent')\n", "plt.xlabel('Year')\n", "plt.ylabel('Life Expectancy')\n", "plt.legend()\n", "plt.show()\n", "\n", "# Interactive Box Plot: Distribution of Tips by Day with Hover Effects\n", "plt.figure(figsize=(10, 6))\n", "tips_df.boxplot(column='tip', by='day', grid=False)\n", "plt.title('Interactive Distribution of Tips by Day')\n", "plt.suptitle('')\n", "plt.xlabel('Day')\n", "plt.ylabel('Tip')\n", "plt.show()\n", "\n", "# Interactive Scatter Matrix: Iris Dataset with Hover Effects\n", "scatter_matrix(iris_df, figsize=(12, 8), diagonal='kde', color=iris_df['species'].astype('category').cat.codes)\n", "plt.suptitle('Interactive Scatter Matrix of Iris Dataset')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [""]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}