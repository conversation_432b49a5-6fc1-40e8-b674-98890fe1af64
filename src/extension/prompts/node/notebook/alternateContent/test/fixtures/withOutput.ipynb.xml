<VSCode.Cell id="798e67a8" language="markdown">
# Sample Notebook
</VSCode.Cell>
<VSCode.Cell id="b7b9f5b3" language="markdown">
## First cell contains imports and second cell some helper functions
</VSCode.Cell>
<VSCode.Cell id="39d80b2d" language="python">
import sys
import os
</VSCode.Cell>
<VSCode.Cell id="a9efc313" language="python">
def do_something():
    print("Hello from Python!")
    print("The current working directory is: " + os.getcwd())
</VSCode.Cell>
<VSCode.Cell id="c894b357" language="markdown">
## Print hello world
</VSCode.Cell>
<VSCode.Cell id="d498d31b" language="python">
print("Hello World")
print(sys.executable)
</VSCode.Cell>
<VSCode.Cell id="61658227" language="markdown">
## Cell with trailing empty lines
</VSCode.Cell>
<VSCode.Cell id="2e7687de" language="python">
print("Python version")






</VSCode.Cell>
<VSCode.Cell id="c27401dd" language="python">

</VSCode.Cell>