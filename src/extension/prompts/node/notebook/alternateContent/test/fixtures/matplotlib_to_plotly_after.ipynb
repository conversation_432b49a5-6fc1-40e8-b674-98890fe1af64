{"cells": [{"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["# Iris Data Analysis\n", "This notebook shows data exploration with Plotly and Pandas."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import plotly.express as px\n", "from plotly.data import iris"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["df = iris()\n", "df.rename(columns={'sepal_width':'Sepal_Width'}, inplace=True)\n", "unique_species = df['species'].unique()"]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["## Scatter Plot: Sepal Length vs Sepal Width\n", "This plot shows the relationship between Sepal Length and Sepal Width for different species."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Scatter Plot: Sepal Length vs Sepal Width\n", "fig = px.scatter(df, x='Sepal_Width', y='sepal_length', color='species', \n", "                 title='Sepal Length vs Sepal Width', labels={'sepal_length':'Sepal Length', 'Sepal_Width':'Sepal Width'})\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["## Bar Chart: Species Counts\n", "This chart shows the count of each species in the dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Bar Chart: Species Counts\n", "species_counts = df['species'].value_counts()\n", "fig = px.bar(species_counts, x=species_counts.index, y=species_counts.values, \n", "             title='Species Counts', labels={'index':'Species', 'y':'Count'}, color=species_counts.index)\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["## Histogram: Sepal Length Distribution\n", "This histogram shows the distribution of Sepal Length in the dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Histogram: Sepal Length Distribution\n", "fig = px.histogram(df, x='sepal_length', nbins=20, title='Distribution of Sepal Length', labels={'sepal_length':'Sepal Length'})\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["## Pie Chart: Species Distribution\n", "This pie chart shows the distribution of different species in the dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Pie Chart: Species Distribution\n", "fig = px.pie(df, names='species', title='Species Distribution')\n", "fig.show()"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}