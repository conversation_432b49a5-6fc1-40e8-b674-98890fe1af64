{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Introduction\n", "\n", "In this notebook, we perform a basic exploratory analysis of the Iris dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Imports\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# Load dataset\n", "# (Imaginary code to load iris_data, assuming it's in memory already)\n", "iris_data = [\n", "    [5.1, 3.5, 1.4, 0.2, \"setosa\"],\n", "    [7.0, 3.2, 4.7, 1.4, \"versicolor\"],\n", "    [6.3, 3.3, 6.0, 2.5, \"virginica\"]\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Simple descriptive stats\n", "sepal_lengths = [row[0] for row in iris_data]\n", "print(\"Average sepal length:\", np.mean(sepal_lengths))\n", "\n", "sepal_widths = [row[1] for row in iris_data]\n", "print(\"Average sepal width:\", np.mean(sepal_widths))"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}