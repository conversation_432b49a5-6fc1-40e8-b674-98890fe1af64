{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "p = np.linspace(0, 20, 100)\n", "plt.plot(p, np.sin(15 * p))\n", "plt.show()\n", ""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create an example dataframe about a fictional army\n", "raw_data = {'regiment': ['Nighthawks', 'Nighthawks', 'Nighthawks', 'Nighthawks', 'Dragoons', 'Dragoons', 'Dragoons', 'Dragoons', 'Scouts', 'Scouts', 'Scouts', 'Scouts'],\n", "            'company': ['1st', '1st', '2nd', '2nd', '1st', '1st', '2nd', '2nd','1st', '1st', '2nd', '2nd'],\n", "            'deaths': [523, 52, 25, 616, 43, 234, 523, 62, 62, 73, 37, 35],\n", "            'battles': [5, 42, 2, 2, 4, 7, 8, 3, 4, 7, 8, 9],\n", "            'size': [1045, 957, 1099, 1400, 1592, 1006, 987, 849, 973, 1005, 1099, 1523],\n", "            'veterans': [1, 5, 62, 26, 73, 37, 949, 48, 48, 435, 63, 345],\n", "            'readiness': [1, 2, 3, 3, 2, 1, 2, 3, 2, 1, 2, 3],\n", "            'armored': [1, 0, 1, 1, 0, 1, 0, 1, 0, 0, 1, 1],\n", "            'deserters': [4, 24, 31, 2, 3, 4, 24, 31, 2, 3, 2, 3],\n", "            'origin': ['Arizona', 'California', 'Texas', 'Florida', 'Maine', 'Iowa', 'Alaska', 'Washington', 'Oregon', 'Wyoming', 'Louisana', 'Georgia']}\n", "\n", "army = pd.DataFrame(data=raw_data)\n", "army.set_index('origin', inplace=True)\n", ""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["raw_data = {'regiment': ['Nighthawks', 'Nighthawks', 'Nighthawks', 'Nighthawks', 'Dragoons', 'Dragoons', 'Dragoons', 'Dragoons', 'Scouts', 'Scouts', 'Scouts', 'Scouts'],\n", "        'company': ['1st', '1st', '2nd', '2nd', '1st', '1st', '2nd', '2nd','1st', '1st', '2nd', '2nd'],\n", "        'name': ['<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>'],\n", "        'preTestScore': [4, 24, 31, 2, 3, 4, 24, 31, 2, 3, 2, 3],\n", "        'postTestScore': [25, 94, 57, 62, 70, 25, 94, 57, 62, 70, 62, 70]}\n", "regiment = pd.DataFrame(raw_data, columns = raw_data.keys())\n", ""]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}