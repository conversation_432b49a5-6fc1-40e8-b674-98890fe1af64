{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create and populate a 5x2 NumPy array.\n", "my_data = np.array([[0, 3], [10, 7], [20, 9], [30, 14], [40, 15]])\n", "\n", "# Create a Python list that holds the names of the two columns.\n", "my_column_names = ['temperature', 'activity']\n", "\n", "# Create a DataFrame.\n", "my_dataframe = pd.DataFrame(data=my_data, columns=my_column_names)\n", "\n", "# Print the entire DataFrame\n", "print(my_dataframe)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}