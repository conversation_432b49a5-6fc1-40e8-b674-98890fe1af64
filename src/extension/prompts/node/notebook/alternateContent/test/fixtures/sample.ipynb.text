#%% vscode.cell [id=2792efb1] [language=markdown]
"""
# Sample Notebook
"""
#%% vscode.cell [id=d3c4c795] [language=markdown]
"""
## First cell contains imports and second cell some helper functions
"""
#%% vscode.cell [id=411446bb] [language=python]
import sys
import os
#%% vscode.cell [id=97965cf2] [language=python]
def do_something():
    print("Hello from Python!")
    print("The current working directory is: " + os.getcwd())
#%% vscode.cell [id=37fd0896] [language=markdown]
"""
## Print hello world
"""
#%% vscode.cell [id=40eb234e] [language=python]
print("Hello World")
print(sys.executable)
#%% vscode.cell [id=d0c31805] [language=markdown]
"""
## Cell with trailing empty lines
"""
#%% vscode.cell [id=76502755] [language=python]
print("Python version")






#%% vscode.cell [id=ae905e06] [language=python]