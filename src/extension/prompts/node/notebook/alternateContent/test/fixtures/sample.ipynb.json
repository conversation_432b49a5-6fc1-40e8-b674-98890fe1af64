{"cells": [{"cell_type": "markdown", "id": "2ce940c2", "metadata": {"language": "markdown"}, "source": ["# Sample Notebook"]}, {"cell_type": "markdown", "id": "9ad0b93a", "metadata": {"language": "markdown"}, "source": ["## First cell contains imports and second cell some helper functions"]}, {"cell_type": "code", "id": "7e6a1e01", "metadata": {"language": "python"}, "source": ["import sys", "import os"]}, {"cell_type": "code", "id": "a45c99f0", "metadata": {"language": "python"}, "source": ["def do_something():", "    print(\"Hello from <PERSON>!\")", "    print(\"The current working directory is: \" + os.getcwd())"]}, {"cell_type": "markdown", "id": "8fef560b", "metadata": {"language": "markdown"}, "source": ["## Print hello world"]}, {"cell_type": "code", "id": "719706c6", "metadata": {"language": "python"}, "source": ["print(\"Hello World\")", "print(sys.executable)"]}, {"cell_type": "markdown", "id": "8b374fb4", "metadata": {"language": "markdown"}, "source": ["## Cell with trailing empty lines"]}, {"cell_type": "code", "id": "fa4a4c9d", "metadata": {"language": "python"}, "source": ["print(\"Python version\")", "", "", "", "", "", ""]}, {"cell_type": "code", "id": "1f4853d6", "metadata": {"language": "python"}, "source": [""]}]}