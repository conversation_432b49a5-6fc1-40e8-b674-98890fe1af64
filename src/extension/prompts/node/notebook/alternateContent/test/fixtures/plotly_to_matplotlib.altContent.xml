<VSCode.Cell id="<CELL_ID_0>" language="markdown">
# Import Required Libraries
Import the necessary libraries, including pandas and matplotlib.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_1>" language="python">
# Import Required Libraries
import pandas as pd
import matplotlib.pyplot as plt

# Import plotly data
import plotly.data as data
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_2>" language="markdown">
# Load Sample Data from plotly.data
Load sample datasets from the plotly.data package into pandas DataFrames.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_3>" language="python">
# Load Sample Data from plotly.data

# Load the 'gapminder' dataset into a pandas DataFrame
gapminder_df = data.gapminder()

# Load the 'tips' dataset into a pandas DataFrame
tips_df = data.tips()

# Load the 'iris' dataset into a pandas DataFrame
iris_df = data.iris()

# Display the first few rows of each DataFrame
gapminder_df.head(), tips_df.head(), iris_df.head()
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_4>" language="markdown">
# DataFrame Operations
Perform various DataFrame operations such as filtering, grouping, and merging.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_5>" language="python">
# DataFrame Operations

# Filtering: Select rows where the year is 2007 in the gapminder dataset
gapminder_2007 = gapminder_df[gapminder_df['year'] == 2007]
gapminder_2007.head()

# Grouping: Group the tips dataset by day and calculate the average tip
average_tips_by_day = tips_df.groupby('day')['tip'].mean().reset_index()
average_tips_by_day

# Merging: Merge the iris dataset with itself on the species column
merged_iris = pd.merge(iris_df, iris_df, on='species', suffixes=('_left', '_right'))
merged_iris.head()
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_6>" language="markdown">
# Descriptive Statistics
Calculate descriptive statistics for the DataFrames, including mean, median, and standard deviation.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_7>" language="markdown">
# Data Visualization with Matplotlib
Create static plots using Matplotlib to visualize the data, including bar charts, scatter plots, and line charts.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_8>" language="python">
# Data Visualization with Matplotlib

# Bar Chart: Average Tips by Day
plt.figure(figsize=(10, 6))
plt.bar(average_tips_by_day['day'], average_tips_by_day['tip'], color='skyblue')
plt.title('Average Tips by Day')
plt.xlabel('Day')
plt.ylabel('Average Tip')
plt.show()

# Scatter Plot: Life Expectancy vs. GDP per Capita (2007)
plt.figure(figsize=(10, 6))
plt.scatter(gapminder_2007['gdpPercap'], gapminder_2007['lifeExp'], c=gapminder_2007['continent'].astype('category').cat.codes, s=gapminder_2007['pop'] / 1e6, alpha=0.5)
plt.xscale('log')
plt.title('Life Expectancy vs. GDP per Capita (2007)')
plt.xlabel('GDP per Capita')
plt.ylabel('Life Expectancy')
plt.show()

# Line Chart: Life Expectancy Over Time for Each Continent
plt.figure(figsize=(10, 6))
for continent, group in gapminder_df.groupby('continent'):
    plt.plot(group['year'], group['lifeExp'], label=continent)
plt.title('Life Expectancy Over Time for Each Continent')
plt.xlabel('Year')
plt.ylabel('Life Expectancy')
plt.legend()
plt.show()

# Scatter Matrix: Iris Dataset
from pandas.plotting import scatter_matrix
scatter_matrix(iris_df, figsize=(12, 8), diagonal='kde', color=iris_df['species'].astype('category').cat.codes)
plt.suptitle('Scatter Matrix of Iris Dataset')
plt.show()

# Box Plot: Distribution of Tips by Day
plt.figure(figsize=(10, 6))
tips_df.boxplot(column='tip', by='day', grid=False)
plt.title('Distribution of Tips by Day')
plt.suptitle('')
plt.xlabel('Day')
plt.ylabel('Tip')
plt.show()
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_9>" language="markdown">
# Interactive Plots with Matplotlib
Create interactive plots using Matplotlib to explore the data, including hover effects and zooming.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_10>" language="python">
# Interactive Plots with Matplotlib

# Interactive Scatter Plot: Life Expectancy vs. GDP per Capita (2007) with Hover Effects
plt.figure(figsize=(10, 6))
scatter = plt.scatter(gapminder_2007['gdpPercap'], gapminder_2007['lifeExp'], c=gapminder_2007['continent'].astype('category').cat.codes, s=gapminder_2007['pop'] / 1e6, alpha=0.5)
plt.xscale('log')
plt.title('Interactive Life Expectancy vs. GDP per Capita (2007)')
plt.xlabel('GDP per Capita')
plt.ylabel('Life Expectancy')
plt.show()

# Interactive Line Chart: Life Expectancy Over Time for Each Continent with Zooming
plt.figure(figsize=(10, 6))
for continent, group in gapminder_df.groupby('continent'):
    plt.plot(group['year'], group['lifeExp'], label=continent)
plt.title('Interactive Life Expectancy Over Time for Each Continent')
plt.xlabel('Year')
plt.ylabel('Life Expectancy')
plt.legend()
plt.show()

# Interactive Box Plot: Distribution of Tips by Day with Hover Effects
plt.figure(figsize=(10, 6))
tips_df.boxplot(column='tip', by='day', grid=False)
plt.title('Interactive Distribution of Tips by Day')
plt.suptitle('')
plt.xlabel('Day')
plt.ylabel('Tip')
plt.show()

# Interactive Scatter Matrix: Iris Dataset with Hover Effects
scatter_matrix(iris_df, figsize=(12, 8), diagonal='kde', color=iris_df['species'].astype('category').cat.codes)
plt.suptitle('Interactive Scatter Matrix of Iris Dataset')
plt.show()
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_11>" language="python">

</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_12>" language="python">

</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_13>" language="python">

</VSCode.Cell>