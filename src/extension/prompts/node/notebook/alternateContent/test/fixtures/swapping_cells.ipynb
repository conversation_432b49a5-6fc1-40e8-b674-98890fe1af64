{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["0"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["1"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["3"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["4"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["5"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["6"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["7"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["8"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["9"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}