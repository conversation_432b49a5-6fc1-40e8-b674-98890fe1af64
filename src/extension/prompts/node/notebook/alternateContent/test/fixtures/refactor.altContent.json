{"cells": [{"cell_type": "markdown", "id": "CELL_ID_0", "metadata": {"language": "markdown"}, "source": ["# Customer Purchase Analysis", "This notebook analyzes customer purchases for an e-commerce store."]}, {"cell_type": "code", "id": "CELL_ID_1", "metadata": {"language": "python"}, "source": ["# Data stored in a dictionary keyed by customer_id", "purchases = {", "    101: [(2001, 2, 19.99), (2003, 3, 10.99)],", "    102: [(2002, 1, 5.49)],", "    103: [(2002, 2, 5.49)]", "}", "", "def get_total_spend(purchase_dict, customer_id):", "    total = 0", "    if customer_id in purchase_dict:", "        for product_id, quantity, price in purchase_dict[customer_id]:", "            total += quantity * price", "    return total", "", "print(\"Customer 101 total spend:\", get_total_spend(purchases, 101))"]}, {"cell_type": "markdown", "id": "CELL_ID_2", "metadata": {"language": "markdown"}, "source": ["## Next Steps", "We'll look for patterns in product popularity and customer spend over time."]}]}