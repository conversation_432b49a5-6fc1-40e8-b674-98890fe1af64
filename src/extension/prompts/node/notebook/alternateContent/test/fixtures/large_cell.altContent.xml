<VSCode.Cell id="<CELL_ID_0>" language="markdown">
# 1. Introduction
This notebook demonstrates a large code cell with multiple functions.
We will later update a few lines in the middle of that big cell.
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_1>" language="python">
import pandas as pd
import numpy as np

# Below is a large cell with 100+ lines of code.
# We'll define multiple functions for demonstration.
# Lines are intentionally verbose/filler to reach 100+.

def load_data() -> pd.DataFrame:
    """
    Simulate loading data from a source.

    This function creates a pandas DataFrame with some sample data.
    The data includes three columns:
    - 'A': Contains integer values with one missing value.
    - 'B': Contains integer values with one missing value.
    - 'C': Contains categorical values with one missing value.

    The purpose of this function is to simulate the process of loading data
    from an external source such as a CSV file, database, or API. In a real-world
    scenario, this function would contain the logic to read data from the actual
    data source and return it as a pandas DataFrame.

    Returns:
        pd.DataFrame: A DataFrame containing the sample data with columns 'A', 'B', and 'C'.
    """
    data = {
        'A': [1, 2, 3, None, 5],
        'B': [10, 9, None, 7, 6],
        'C': ['x', 'y', None, 'y', 'x']
    }
    df = pd.DataFrame(data)
    return df

def clean_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Clean the dataframe by filling missing values and performing additional cleaning steps.

    This function takes a pandas DataFrame as input and performs the following cleaning operations:
    1. Identifies numeric columns and fills missing values with the mean of the respective column.
    2. Identifies categorical columns and fills missing values with the string 'missing'.
    3. Replaces any negative values in column 'B' with 0.

    The purpose of this function is to ensure that the DataFrame is free of missing values and
    any negative values in column 'B' are handled appropriately. This is a common step in data
    preprocessing to prepare the data for further analysis or modeling.

    Args:
        df (pd.DataFrame): The input DataFrame that needs to be cleaned.

    Returns:
        pd.DataFrame: The cleaned DataFrame with no missing values and negative values in column 'B' replaced with 0.
    """
    # Some placeholder logic for cleaning.
    # Let's say we fill numeric columns with mean.
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    for col in numeric_cols:
        df[col] = df[col].fillna(df[col].mean())

    # Fill categorical columns with a placeholder
    object_cols = df.select_dtypes(include=['object']).columns
    for col in object_cols:
        df[col] = df[col].fillna('missing')

    # Additional cleaning steps:
    # Replace negative values in column B with 0.
    if 'B' in df.columns:
        df.loc[df['B'] < 0, 'B'] = 0

    # return the result
    return df

def transform_data(df: pd.DataFrame) -> pd.DataFrame:
    """
    Perform data transformation on the DataFrame.

    This function takes a pandas DataFrame as input and performs several transformation operations:
    1. Creates a new column 'A_squared' which contains the square of the values in column 'A'.
    2. Creates a new column 'B_plus_one' which contains the values in column 'B' incremented by 1.

    The purpose of this function is to enhance the DataFrame with additional features that may be useful
    for further analysis or modeling. By creating new columns based on existing data, we can provide
    more information to downstream processes and potentially improve the performance of machine learning models.

    Args:
        df (pd.DataFrame): The input DataFrame that needs to be transformed.

    Returns:
        pd.DataFrame: The transformed DataFrame with new columns 'A_squared' and 'B_plus_one'.
    """
    # Some transformation placeholder
    df['A_squared'] = df['A'] ** 2
    df['B_plus_one'] = df['B'] + 1
    return df

def feature_engineer(df: pd.DataFrame) -> pd.DataFrame:
    """
    Perform additional feature engineering on the DataFrame.

    This function takes a pandas DataFrame as input and performs several additional feature engineering operations:
    1. Creates a new column 'AB_ratio' which contains the ratio of the values in column 'A' to the values in column 'B'.
       - To avoid division by zero, a small constant (0.001) is added to the denominator.
    2. Creates a new column 'is_x' which is a binary indicator:
       - The value is 1 if the corresponding value in column 'C' is 'x'.
       - The value is 0 otherwise.

    The purpose of this function is to create new features that may provide additional insights or improve the performance
    of machine learning models. By engineering new features based on existing data, we can capture more complex relationships
    and patterns that may not be immediately apparent from the original columns.

    Args:
        df (pd.DataFrame): The input DataFrame that needs further feature engineering.

    Returns:
        pd.DataFrame: The DataFrame with additional engineered features 'AB_ratio' and 'is_x'.
    """
    df['AB_ratio'] = df['A'] / (df['B'] + 0.001)
    df['is_x'] = df['C'].apply(lambda x: 1 if x == 'x' else 0)
    return df

def run_pipeline():
    """
    Orchestrates all steps of the data processing pipeline.

    This function serves as the main entry point for executing the entire data processing pipeline.
    It performs the following steps in sequence to transform raw data into a cleaned and feature-engineered DataFrame:

    1. Load Data:
       - Calls the `load_data` function to simulate loading data from an external source.
       - The data is loaded into a pandas DataFrame with columns 'A', 'B', and 'C'.
       - The initial data contains some missing values and is used as the starting point for further processing.

    2. Clean Data:
       - Calls the `clean_data` function to clean the loaded DataFrame.
       - Missing values in numeric columns are filled with the mean of the respective column.
       - Missing values in categorical columns are filled with the string 'missing'.
       - Any negative values in column 'B' are replaced with 0.
       - The cleaned DataFrame is returned for the next step.

    3. Transform Data:
       - Calls the `transform_data` function to perform data transformation on the cleaned DataFrame.
       - A new column 'A_squared' is created, containing the square of the values in column 'A'.
       - A new column 'B_plus_one' is created, containing the values in column 'B' incremented by 1.
       - The transformed DataFrame is returned for the next step.

    4. Feature Engineer:
       - Calls the `feature_engineer` function to perform additional feature engineering on the transformed DataFrame.
       - A new column 'AB_ratio' is created, containing the ratio of the values in column 'A' to the values in column 'B'.
         - To avoid division by zero, a small constant (0.001) is added to the denominator.
       - A new column 'is_x' is created, which is a binary indicator:
         - The value is 1 if the corresponding value in column 'C' is 'x'.
         - The value is 0 otherwise.
       - The feature-engineered DataFrame is returned as the final output.

    Returns:
        pd.DataFrame: The final DataFrame after all processing steps, including cleaning, transformation, and feature engineering.
    """
    data = load_data()
    data = clean_data(data)
    data = transform_data(data)
    data = feature_engineer(data)
    return data
</VSCode.Cell>
<VSCode.Cell id="<CELL_ID_2>" language="python">
# 3. Usage Example
df_pipeline = run_pipeline()
df_pipeline
</VSCode.Cell>