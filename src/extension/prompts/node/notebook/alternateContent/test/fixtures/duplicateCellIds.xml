<VSCode.Cell id="ba601542" language="markdown">
# Iris Data Analysis
This notebook shows data exploration with <PERSON><PERSON>ly and Pandas.
</VSCode.Cell>
<VSCode.Cell id="16ca5e48" language="python">
import numpy as np
import pandas as pd
import plotly.express as px
from plotly.data import iris
</VSCode.Cell>
<VSCode.Cell id="e1a6dd6d" language="python">
df = iris()
df.rename(columns={'sepal_width':'Sepal_Width'}, inplace=True)
unique_species = df['species'].unique()
</VSCode.Cell>
<VSCode.Cell id="4a116467" language="markdown">
# Scatter Plot: Sepal Length vs Sepal Width
This plot shows the relationship between Sepal Length and Sepal Width for different species.
</VSCode.Cell>
<VSCode.Cell id="4a116467" language="python">
fig = px.scatter(df, x='Sepal_Width', y='sepal_length', color='species',
                 title='Sepal Length vs Sepal Width', labels={'Sepal_Width':'Sepal Width', 'sepal_length':'Sepal Length'})
fig.show()
</VSCode.Cell>
<VSCode.Cell id="26fd9a7e" language="markdown">
# Bar Chart: Species Counts
This bar chart shows the count of each species in the dataset.
</VSCode.Cell>
<VSCode.Cell id="26fd9a7e" language="python">
species_counts = df['species'].value_counts()
fig = px.bar(species_counts, x=species_counts.index, y=species_counts.values,
             title='Species Counts', labels={'x':'Species', 'y':'Count'}, color=species_counts.index)
fig.show()
</VSCode.Cell>
<VSCode.Cell id="24eac482" language="markdown">
# Histogram: Sepal Length Distribution
This histogram shows the distribution of Sepal Length in the dataset.
</VSCode.Cell>
<VSCode.Cell id="24eac482" language="python">
fig = px.histogram(df, x='sepal_length', nbins=20, title='Distribution of Sepal Length', labels={'sepal_length':'Sepal Length'})
fig.show()
</VSCode.Cell>
<VSCode.Cell id="new_pie_chart" language="markdown">
# Pie Chart: Species Distribution
This pie chart shows the distribution of different species in the dataset.
</VSCode.Cell>
<VSCode.Cell id="new_pie_chart" language="python">
fig = px.pie(df, names='species', title='Species Distribution')
fig.show()
</VSCode.Cell>