{"cells": [{"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["# Customer Purchase Analysis\n", "This notebook analyzes customer purchases for an e-commerce store."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Data stored in a dictionary keyed by customer_id\n", "purchases = {\n", "    101: [(2001, 2, 19.99), (2003, 3, 10.99)],\n", "    102: [(2002, 1, 5.49)],\n", "    103: [(2002, 2, 5.49)]\n", "}\n", "\n", "def get_total_spend(purchase_dict, customer_id):\n", "    total = 0\n", "    if customer_id in purchase_dict:\n", "        for product_id, quantity, price in purchase_dict[customer_id]:\n", "            total += quantity * price\n", "    return total\n", "\n", "print(\"Customer 101 total spend:\", get_total_spend(purchases, 101))"]}, {"cell_type": "markdown", "metadata": {}, "outputs": [], "source": ["## Next Steps\n", "We'll look for patterns in product popularity and customer spend over time."]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}