{"cells": [{"cell_type": "markdown", "metadata": {"id": "1"}, "source": ["# Sample Notebook"]}, {"cell_type": "markdown", "metadata": {"id": "2"}, "source": ["## First cell contains imports and second cell some helper functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "3"}, "outputs": [], "source": ["import sys\n", "import os"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "4"}, "outputs": [], "source": ["def do_something():\n", "    print(\"Hello from <PERSON>!\")\n", "    print(\"The current working directory is: \" + os.getcwd())"]}, {"cell_type": "markdown", "metadata": {"id": "5"}, "source": ["## Print hello world"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "6"}, "outputs": [], "source": ["print(\"Hello World\")\n", "print(sys.executable)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Cell with trailing empty lines"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"Python version\")\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.9.6"}}, "nbformat": 4, "nbformat_minor": 2}