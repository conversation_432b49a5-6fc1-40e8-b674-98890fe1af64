{"cells": [{"cell_type": "markdown", "id": "CELL_ID_0", "metadata": {"language": "markdown"}, "source": ["# Data Processing Notebook", "", "This notebook demonstrates a pipeline for processing and analyzing customer sales data."]}, {"cell_type": "code", "id": "CELL_ID_1", "metadata": {"language": "python"}, "source": ["import pandas as pd", "import numpy as np"]}, {"cell_type": "markdown", "id": "CELL_ID_2", "metadata": {"language": "markdown"}, "source": ["## Data Loading"]}, {"cell_type": "code", "id": "CELL_ID_3", "metadata": {"language": "python"}, "source": ["data = pd.DataFrame({", "    'customer_id': [101, 102, 103],", "    'sales': [250.0, 130.0, 400.0],", "    'region': ['North', 'East', 'West']", "})", "data.head()"]}, {"cell_type": "markdown", "id": "CELL_ID_4", "metadata": {"language": "markdown"}, "source": ["## Data Processing"]}, {"cell_type": "code", "id": "CELL_ID_5", "metadata": {"language": "python"}, "source": ["def process_data(df, region_filter=None, normalize=False):", "    \"\"\"", "    Process customer sales data.", "", "    Args:", "        df (pd.DataFrame): The input DataFrame containing customer data.", "        region_filter (str, optional): A region to filter data. Defaults to None.", "        normalize (bool, optional): Whether to normalize sales data. Defaults to False.", "", "    Returns:", "        pd.DataFrame: Processed data with added total sales.", "    \"\"\"", "    if region_filter:", "        df = df[df['region'] == region_filter]", "", "    if normalize:", "        df['sales'] = (df['sales'] - df['sales'].min()) / (df['sales'].max() - df['sales'].min())", "", "    # Add a new column for cumulative sales", "    df['cumulative_sales'] = df['sales'].cumsum()", "    return df"]}, {"cell_type": "code", "id": "CELL_ID_6", "metadata": {"language": "python"}, "source": ["# Process the entire dataset without filtering", "all_data = process_data(data)", "all_data"]}, {"cell_type": "code", "id": "CELL_ID_7", "metadata": {"language": "python"}, "source": ["# Filter and process data for the North region with normalization", "north_data = process_data(data, region_filter='North', normalize=True)", "north_data"]}], "metadata": {"kernelspec": {"language": "python"}, "language_info": {"name": "python"}}}