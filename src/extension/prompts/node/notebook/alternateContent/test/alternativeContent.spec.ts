/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { EOL } from 'os';
import { describe, expect, test } from 'vitest';
import type { NotebookDocument } from 'vscode';
import { DiffServiceImpl } from '../../../../../../platform/diff/node/diffServiceImpl';
import { ILogger, ILogService } from '../../../../../../platform/log/common/logService';
import { IAlternativeNotebookContentService } from '../../../../../../platform/notebook/common/alternativeContent';
import { AlternativeNotebookContentEditGenerator, textToAsyncIterableLines } from '../../../../../../platform/notebook/common/alternativeContentEditGenerator';
import { BaseAlternativeNotebookContentProvider } from '../../../../../../platform/notebook/common/alternativeContentProvider';
import { AlternativeJsonNotebookContentProvider } from '../../../../../../platform/notebook/common/alternativeContentProvider.json';
import { AlternativeTextNotebookContentProvider } from '../../../../../../platform/notebook/common/alternativeContentProvider.text';
import { AlternativeXmlNotebookContentProvider } from '../../../../../../platform/notebook/common/alternativeContentProvider.xml';
import { notebookCellToCellData, summarize } from '../../../../../../platform/notebook/common/helpers';
import { NullTelemetryService } from '../../../../../../platform/telemetry/common/nullTelemetryService';
import { SimulationWorkspace } from '../../../../../../platform/test/node/simulationWorkspace';
import { ExtHostNotebookDocumentData } from '../../../../../../util/common/test/shims/notebookDocument';
import { AsyncIterableObject } from '../../../../../../util/vs/base/common/async';
import { CancellationToken } from '../../../../../../util/vs/base/common/cancellation';
import { ResourceMap } from '../../../../../../util/vs/base/common/map';
import * as path from '../../../../../../util/vs/base/common/path';
import { NotebookCellData, NotebookCellKind, NotebookData, NotebookEdit, NotebookRange, Position, Range, TextEdit, Uri } from '../../../../../../vscodeTypes';
import { LineOfText } from '../../../../../prompt/node/streamingEdits';
import { fixture, loadFile, loadNotebook } from './utils';

describe('Alternative Content for Notebooks', () => {
	[
		new AlternativeXmlNotebookContentProvider(),
		new AlternativeTextNotebookContentProvider(),
		new AlternativeJsonNotebookContentProvider()
	].forEach((provider) => {
		const mockLogger: ILogger = {
			error: () => { /* no-op */ },
			warn: () => { /* no-op */ },
			info: () => { /* no-op */ },
			debug: () => { /* no-op */ },
			trace: () => { /* no-op */ },
			show: () => { /* no-op */ }
		};
		function getEditGenerator(provider: BaseAlternativeNotebookContentProvider) {
			return new AlternativeNotebookContentEditGenerator(new class implements IAlternativeNotebookContentService {
				declare readonly _serviceBrand: undefined;
				create(_format: any) {
					return provider;
				}
				getFormat() {
					return provider.kind;
				}
			}(), new DiffServiceImpl(), new class implements ILogService {
				_serviceBrand: undefined;
				internal = mockLogger;
				logger = mockLogger;
				showPublicLog(preserveFocus?: boolean): void {
					//
				}
			}(), new NullTelemetryService());
		}
		[true, false].forEach(applyEditsImmediately => {
			describe(`${provider.kind} Content Parser`, () => {
				test(`Generate a single Notebook Edit (insert md cell)`, async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const alternativeFile = await loadFile({ filePath: `${fixture('insert.1.ipynb')}.xml` });
					const file = await loadFile({ filePath: fixture('insert.ipynb') });
					const notebook = await loadNotebook(file);

					let alternativeContents = alternativeFile.contents;
					const cellSummary = notebook.getCells().map(summarize);
					cellSummary.forEach(cell => {
						const toReplace = provider.kind === 'xml' ? `<CELL_ID_${cell.index}>` : `CELL_ID_${cell.index}`;
						alternativeContents = alternativeContents.replace(toReplace, cell.id);
					});
					const alternativeContentLines = AsyncIterableObject.fromArray(alternativeContents.split(/\r?\n/)).map(l => new LineOfText(l));
					const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, alternativeContentLines, undefined, CancellationToken.None);
					const notebookEdits: NotebookEdit[] = [];
					for await (const edit of edits) {
						if (!Array.isArray(edit)) {
							notebookEdits.push(edit);
						}
					}
					expect(notebookEdits.length).toBe(1);
					expect(notebookEdits[0].newCells.length).toBe(1);
					expect(notebookEdits[0].newCells[0].kind).toBe(NotebookCellKind.Markup);
					expect(notebookEdits[0].newCells[0].value.split(/\r?\n/g)).toEqual([`# DataFrame Details`, ``, `This DataFrame contains two columns: 'Name' and 'Gender'. The 'Name' column has three entries: 'Hello', 'World', and 'Baz'. The 'Gender' column has three entries: 'F', 'M', and 'F'.`]);
					expect(notebookEdits[0].range.start).toBe(1);
					expect(notebookEdits[0].range.end).toBe(1);

					// Generate edits as though this is a branch new notebook.
					const newEdits = await getEditGenerator(provider).generateNotebookEdits(Uri.file('newNotebook.ipynb'), alternativeContentLines, undefined, CancellationToken.None);
					notebookEdits.length = 0;
					for await (const edit of newEdits) {
						if (!Array.isArray(edit)) {
							notebookEdits.push(edit);
						}
					}
					expect(notebookEdits.length).toBe(notebook.cellCount + 1);
				});
				test(`Generate a single Notebook Edit (insert Python cell)`, async () => {
					// This test focuses on generating as Notebook edit where LLM hallucinates
					// & generates Python content instead of a structured Jupytext content.
					// In such cases the python code should be inserted as is in a single cell.
					// Previously nothing would be inserted.
					if (provider.kind !== 'text') {
						return;
					}
					const alternativeContents = 'import math\n\ndef circle_area(radius):\n    return math.pi * radius**2\n';
					const alternativeContentLines = AsyncIterableObject.fromArray(alternativeContents.split(/\r?\n/)).map(l => new LineOfText(l));
					const edits = await getEditGenerator(provider).generateNotebookEdits(Uri.file('newFile.ipynb'), alternativeContentLines, undefined, CancellationToken.None);
					const notebookEdits: NotebookEdit[] = [];
					for await (const edit of edits) {
						if (!Array.isArray(edit)) {
							notebookEdits.push(edit);
						}
					}
					expect(notebookEdits.length).toBe(1);
					expect(notebookEdits[0].newCells.length).toBe(1);
					expect(notebookEdits[0].newCells[0].kind).toBe(NotebookCellKind.Code);
					expect(notebookEdits[0].newCells[0].value.split(/\r?\n/g)).toEqual(alternativeContents.split(/\r?\n/));
				});

				[
					{
						file: `${fixture('insert.2.ipynb')}.xml`,
						notebookEdits: [
							NotebookEdit.insertCells(1, [new NotebookCellData(NotebookCellKind.Markup, '', 'markdown')]),
							NotebookEdit.insertCells(2, [new NotebookCellData(NotebookCellKind.Markup, '', 'markdown')]),
							NotebookEdit.insertCells(7, [new NotebookCellData(NotebookCellKind.Code, '', 'python')])
						]
					},
					{
						file: `${fixture('insert.3.ipynb')}.xml`,
						notebookEdits: [
							NotebookEdit.insertCells(5, [new NotebookCellData(NotebookCellKind.Code, '', 'python')])
						]
					},
					{
						file: `${fixture('insert.4.ipynb')}.xml`,
						notebookEdits: [
							NotebookEdit.deleteCells(new NotebookRange(1, 2))
						]
					}
				].forEach(testInfo => {
					test(`Generate ${testInfo.notebookEdits.length} Notebook Edits from ${path.basename(testInfo.file)}`, async () => {
						// This test focuses on generating as few Notebook edits as possible.
						// If a user deletes a cell in the middle there's no need to generate any other edits, but just the delete edit.
						if (provider.kind !== 'xml') {
							return;
						}

						const simulation = new SimulationWorkspace();
						const beforeIPynb = await loadFile({ filePath: fixture('insert.ipynb') });
						const notebook = await loadNotebook(beforeIPynb, simulation);

						const alternativeFile = await loadFile({ filePath: testInfo.file });
						let alternativeContents = alternativeFile.contents;
						const cellSummary = notebook.getCells().map(summarize);
						cellSummary.forEach(cell => {
							const toReplace = provider.kind === 'xml' ? `<CELL_ID_${cell.index}>` : `CELL_ID_${cell.index}`;
							alternativeContents = alternativeContents.replace(toReplace, cell.id);
						});
						const alternativeContentLines = AsyncIterableObject.fromArray(alternativeContents.split(/\r?\n/)).map(l => new LineOfText(l));
						const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, alternativeContentLines, undefined, CancellationToken.None);


						const notebookEdits: NotebookEdit[] = [];
						for await (const edit of edits) {
							if (Array.isArray(edit)) {
								simulation.applyEdits(edit[0], edit[1]);
							} else {
								notebookEdits.push(edit);
								simulation.applyNotebookEdits(notebook.uri, [edit]);
							}
						}

						expect(normatlizeContent(provider.getAlternativeDocument(notebook).getText())).toBe(normatlizeContent(alternativeFile.contents));
						expect(notebookEdits.length).toBe(testInfo.notebookEdits.length);

						testInfo.notebookEdits.forEach((edit, i) => {
							expect(notebookEdits[i].newCells.length).toBe(edit.newCells.length);
							edit.newCells.forEach((c, j) => {
								expect(notebookEdits[i].newCells[j].kind).toBe(c.kind);
								expect(notebookEdits[i].newCells[j].languageId).toBe(c.languageId);
							});
							expect(notebookEdits[i].range.start).toBe(edit.range.start);
							expect(notebookEdits[i].range.end).toBe(edit.range.end);
						});
					});
				});

				describe(`${provider.kind} Position Translator`, () => {
					test(`Translate position in notebook cell`, async () => {
						const notebook = await loadNotebook(loadFile({ filePath: fixture('sample.ipynb') }));
						const altDoc = provider.getAlternativeDocument(notebook);

						const positions = [
							{ cellIndex: 0, start: new Position(0, 9), end: new Position(0, 17) },
							{ cellIndex: 1, start: new Position(0, 0), end: new Position(0, 34) },
							{ cellIndex: 1, start: new Position(0, 0), end: new Position(0, 33) },
							{ cellIndex: 2, start: new Position(0, 0), end: new Position(0, 6) },
							{ cellIndex: 2, start: new Position(1, 7), end: new Position(1, 9) },
							{ cellIndex: 3, start: new Position(1, 10), end: new Position(2, 9) },
							{ cellIndex: 5, start: new Position(1, 10), end: new Position(1, 20) },
						];

						for (const pos of positions) {
							const cell = notebook.cellAt(pos.cellIndex);
							const startTranslation = [pos.start, pos.end].map(p => altDoc.fromCellPosition(cell.index, p));
							const textFromCell = cell.document.getText(new Range(pos.start, pos.end));
							const textFromAltDoc = altDoc.getText(new Range(startTranslation[0], startTranslation[1]));
							if (provider.kind !== 'json' || pos.start.line === pos.end.line) {
								expect(normatlizeContent(textFromAltDoc)).toBe(normatlizeContent(textFromCell));
							} else {
								expect(normatlizeContent(textFromAltDoc).split(/\r?\n/).join(EOL)).toBe([`\\"Hello from Python!\\")",`, `                "    print`].join(EOL));
							}
						}
					});
				});

				test(`Parse with leading empty lines`, async () => {
					const txt = `

#%% vscode.cell [language=python]
import math

def circle_area(radius):
    return math.pi * radius**2
`;
					const xml = `

<VSCode.Cell id="f18c8b6e" language="python">
import math

def circle_area(radius):
    return math.pi * radius**2
</VSCode.Cell>
`;
					const json = `

{
    "cells": [
        {
            "cell_type": "code",
            "metadata": {
                "id": "f18c8b6e",
                "language": "python"
            },
            "source": [
                "import math",
                "",
                "def circle_area(radius):",
                "    return math.pi * radius**2"
            ]
        }
    ]
}
`;
					const content = provider.kind === 'xml' ? xml : (provider.kind === 'text' ? txt : json);
					const uri = Uri.file('single_before.ipynb');
					const notebook = ExtHostNotebookDocumentData.createJupyterNotebook(uri, JSON.stringify({ cells: [] })).document;
					const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, textToAsyncIterableLines(content), undefined, CancellationToken.None);
					const notebookEdits = [];
					for await (const edit of edits) {
						notebookEdits.push(edit);
					}
					expect(notebookEdits.length).toBe(1);
					expect(notebookEdits[0]).toBeInstanceOf(NotebookEdit);
					expect((notebookEdits[0] as NotebookEdit).newCells.length).toBe(1);
					expect(normatlizeContent((notebookEdits[0] as NotebookEdit).newCells[0].value)).toBe(normatlizeContent(`import math

def circle_area(radius):
    return math.pi * radius**2
`));
				});
				test(`Parse with empty lines between cell markers`, async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const content = `<VSCode.Cell id="feb4cb5e" language="julia">
function circleArea(r::Float64)
    return pi * r * r
end
</VSCode.Cell>


<VSCode.Cell language="julia">
function calculateCircleArea(radius::Float64)
    return pi * radius^2
end
</VSCode.Cell>`;
					const uri = Uri.file('single_before.ipynb');
					const notebook = ExtHostNotebookDocumentData.createJupyterNotebook(uri, JSON.stringify({ cells: [] })).document;
					const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, textToAsyncIterableLines(content), undefined, CancellationToken.None);
					const notebookEdits = [];
					for await (const edit of edits) {
						notebookEdits.push(edit);
					}
					expect(notebookEdits.length).toBe(2);
					expect(notebookEdits[0]).toBeInstanceOf(NotebookEdit);
					expect((notebookEdits[0] as NotebookEdit).newCells.length).toBe(1);
					expect(normatlizeContent((notebookEdits[0] as NotebookEdit).newCells[0].value)).toBe(normatlizeContent(`function circleArea(r::Float64)
    return pi * r * r
end
`));
					expect(normatlizeContent((notebookEdits[1] as NotebookEdit).newCells[0].value)).toBe(normatlizeContent(`function calculateCircleArea(radius::Float64)
    return pi * radius^2
end
`));
				});
				test('Handle duplicate ids', async () => {
					if (provider.kind === 'text' || provider.kind === 'json') {
						return;
					}
					const simulation = new SimulationWorkspace();
					const file = await loadFile({ filePath: fixture('duplicateCellIds.xml') });
					const notebook = await loadNotebook(await loadFile({ filePath: fixture('duplicateCellIds.ipynb') }), simulation);
					const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, textToAsyncIterableLines(file.contents), undefined, CancellationToken.None);
					for await (const edit of edits) {
						if (!Array.isArray(edit)) {
							simulation.applyNotebookEdits(notebook.uri, [edit]);
						}
					}

					expect(notebook.cellCount).toBe(11);
					expect(notebook.getCells()[0].kind).toBe(NotebookCellKind.Markup);
					expect(notebook.getCells()[1].kind).toBe(NotebookCellKind.Code);
					expect(notebook.getCells()[2].kind).toBe(NotebookCellKind.Code);
					expect(notebook.getCells()[3].kind).toBe(NotebookCellKind.Markup);
					expect(notebook.getCells()[4].kind).toBe(NotebookCellKind.Code);
					expect(notebook.getCells()[5].kind).toBe(NotebookCellKind.Markup);
					expect(notebook.getCells()[6].kind).toBe(NotebookCellKind.Code);
					expect(notebook.getCells()[7].kind).toBe(NotebookCellKind.Markup);
					expect(notebook.getCells()[8].kind).toBe(NotebookCellKind.Code);
					expect(notebook.getCells()[9].kind).toBe(NotebookCellKind.Markup);
					expect(notebook.getCells()[10].kind).toBe(NotebookCellKind.Code);
				});
			});
			describe(`${provider.kind} Edit Generation`, () => {
				[
					'circle_area_edits',
					'delete_1_line_in_cell',
					'data_processing',
					'data_processing_2',
					'data_visualization',
					'data_visualization_2',
					'datacleansing',
					'dataframe',
					'edit',
					'empty',
					'imports',
					'large_cell',
					'multicells',
					'plot',
					'plotly_to_matplotlib',
					'refactor',
					'reorder',
					'single',
					'variables'
				].forEach((filePath) => {
					test(`Apply Edits for ${path.basename(filePath)}`, async () => {
						if ((filePath === 'plotly_to_matplotlib' || filePath === 'matplotlib_to_plotly') && provider.kind === 'json') {
							// generating text edits for JSON format and ensuring the final output is the same as that generated for text/xml is difficult.
							return;
						}
						if (provider.kind === 'json' && ['delete_1_line_in_cell'].includes(filePath)) {
							// Incorrectly genrated edits for JSON format.
							return;
						}
						const simulation = new SimulationWorkspace();
						const [atlContent, beforeIPynb, afterIPynb] = await Promise.all([loadFile({ filePath: fixture(`${filePath}.altContent.${provider.kind}`) }), loadFile({ filePath: fixture(`${filePath}_before.ipynb`) }), loadFile({ filePath: fixture(`${filePath}_after.ipynb`) })]);
						const notebook = await loadNotebook(beforeIPynb, simulation);
						const cellSummary = notebook.getCells().map(summarize);
						cellSummary.forEach(cell => {
							const toReplace = provider.kind === 'xml' ? `<CELL_ID_${cell.index}>` : `CELL_ID_${cell.index}`;
							atlContent.contents = atlContent.contents.replace(toReplace, cell.id);
						});

						const notebookEdits: (NotebookEdit | [Uri, TextEdit[]])[] = [];
						for await (const edit of getEditGenerator(provider).generateNotebookEdits(notebook, textToAsyncIterableLines(atlContent.contents), undefined, CancellationToken.None)) {
							notebookEdits.push(edit);
						}

						const notebookData = applyNotebookEdits(notebook, notebookEdits, simulation);
						const expectedNotebook = await loadNotebook(afterIPynb, simulation);
						if (filePath === 'plotly_to_matplotlib' && provider.kind === 'text') {
							// The edits generated for text version is slightly different, hence the result notebook is not the same as we'd expect when using xml.
							// Hence we need to skip the failing cell (due to differences in LLM outputs)
							notebookData.cells[8].value = expectedNotebook.getCells()[8].document.getText();
							notebookData.cells[10].value = expectedNotebook.getCells()[10].document.getText();
						}
						if (filePath === 'multicells' && provider.kind === 'text') {
							// The edits generated for text version is slightly different, hence the result notebook is not the same as we'd expect when using xml.
							// Hence we need to skip the failing cell (due to differences in LLM outputs)
							notebookData.cells[1].value = expectedNotebook.getCells()[1].document.getText();
							notebookData.cells[3].value = expectedNotebook.getCells()[3].document.getText();
						}
						assertDocumentsAreEqual(expectedNotebook, notebookData, provider.kind);
					});
					test(`Generate Edits for New Document for ${path.basename(filePath)}`, async () => {
						if ((filePath === 'plotly_to_matplotlib' || filePath === 'matplotlib_to_plotly') && provider.kind === 'json') {
							// generating text edits for JSON format and ensuring the final output is the same as that generated for text/xml is difficult.
							return;
						}
						const ipynb = await loadFile({ filePath: fixture(`${filePath}_before.ipynb`) });
						const notebook = await loadNotebook(ipynb);
						const altContent = provider.getAlternativeDocument(notebook).getText();
						const alternativeContentLines = textToAsyncIterableLines(altContent);
						const newEdits = await getEditGenerator(provider).generateNotebookEdits(Uri.file('newNotebook.ipynb'), alternativeContentLines, undefined, CancellationToken.None);
						const notebookEdits: NotebookEdit[] = [];
						for await (const edit of newEdits) {
							if (!Array.isArray(edit)) {
								notebookEdits.push(edit);
							}
						}
						expect(notebookEdits.length).toBe(notebook.cellCount);
						notebook.getCells().forEach((cell, i) => {
							const expectedCell = notebook.cellAt(i);
							expect(normatlizeContent(cell.document.getText())).toBe(normatlizeContent(expectedCell.document.getText()));
							expect(cell.document.languageId).toBe(expectedCell.document.languageId);
							expect(cell.kind).toBe(expectedCell.kind);
						});
					});
				});
			});

			/**
			 * In realworld, notebook gets edited asynchronously.
			 * I.e. when we stream the edits, the edits are not applied immediately.
			 * In tests, they get applied immediately.
			 *
			 * Lets cover both cases.
			 */
			async function applyEditsSyncOrAsync(simulation: SimulationWorkspace, notebook: NotebookDocument, edits: AsyncIterable<NotebookEdit | [Uri, TextEdit[]]>, applyEditsImmediately: boolean) {
				const notebookEdits = [];
				if (applyEditsImmediately) {
					for await (const edit of edits) {
						if (Array.isArray(edit)) {
							simulation.applyEdits(edit[0], edit[1]);
						} else {
							simulation.applyNotebookEdits(notebook.uri, [edit]);
							notebookEdits.push(edit);
						}
					}

				} else {
					const collectedEdits = [];
					for await (const edit of edits) {
						collectedEdits.push(edit);
					}
					for (const edit of collectedEdits) {
						if (Array.isArray(edit)) {
							simulation.applyEdits(edit[0], edit[1]);
						} else {
							simulation.applyNotebookEdits(notebook.uri, [edit]);
							notebookEdits.push(edit);
						}
					}
				}
				return notebookEdits;
			}

			describe(`${provider.kind} Generate Edits (insert/delete/swap`, () => {
				async function applyEditsAndVerify(cells: { index: number; contents: number }[]) {
					const simulation = new SimulationWorkspace();
					const notebook = await loadNotebook(await loadFile({ filePath: fixture('swapping_cells.ipynb') }), simulation);
					let altContent = cells.map(item => {
						return [
							`<VSCode.Cell id="<CELL_ID_${item.index}>" language="python">`,
							`${item.contents}`,
							`</VSCode.Cell>`
						].join(EOL);
					}).join(EOL);
					const cellSummary = notebook.getCells().map(summarize);
					cellSummary.forEach(cell => {
						const toReplace = provider.kind === 'xml' ? `<CELL_ID_${cell.index}>` : `CELL_ID_${cell.index}`;
						altContent = altContent.replace(toReplace, cell.id);
					});

					const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, textToAsyncIterableLines(altContent), undefined, CancellationToken.None);
					const notebookEdits = await applyEditsSyncOrAsync(simulation, notebook, edits, applyEditsImmediately);

					expect(notebook.getCells().map(c => c.document.getText()).join()).toBe(cells.map(i => `${i.contents}`).join());
					return { notebook, notebookEdits };
				}
				test('Insert 1 cell at the top', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [10, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9].map(i => ({ index: i, contents: i }));
					const { notebookEdits } = await applyEditsAndVerify(cells);
					expect(notebookEdits.length).toBe(1);
					expect(notebookEdits[0].newCells.length).toBe(1);
					expect(notebookEdits[0].newCells[0].value).toBe('10');
					expect(notebookEdits[0].range.start).toBe(0);
				});
				test('Insert 1 cell at the end', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(i => ({ index: i, contents: i }));
					const { notebookEdits } = await applyEditsAndVerify(cells);
					expect(notebookEdits.length).toBe(1);
					expect(notebookEdits[0].newCells.length).toBe(1);
					expect(notebookEdits[0].newCells[0].value).toBe('10');
				});
				test('Swap 2 Cells', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [0, 1, 2, 3, 4, 5, 6, 7, 9, 8].map(i => ({ index: i, contents: i }));
					await applyEditsAndVerify(cells);
				});
				test('Moving 2 Cells', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [0, 1, 2, 3, 4, 5, 6, 9, 7, 8].map(i => ({ index: i, contents: i }));
					await applyEditsAndVerify(cells);
				});
				test('Delete 1 Cell', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [0, 1, 2, 3, 4, 5, 6, 7, 8].map(i => ({ index: i, contents: i }));
					const { notebookEdits } = await applyEditsAndVerify(cells);

					expect(notebookEdits.length).toBe(1);
					expect(notebookEdits[0].range.start).toBe(9);
				});
				test('Move last Cell to top', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [9, 0, 1, 2, 3, 4, 5, 6, 7, 8].map(i => ({ index: i, contents: i }));
					const { notebookEdits } = await applyEditsAndVerify(cells);

					expect(notebookEdits.length).toBe(2);
					expect(notebookEdits[0].range.start).toBe(9);
					expect(notebookEdits[1].range.start).toBe(0);
					expect(notebookEdits[1].newCells[0].value).toBe('9');
				});
				test('Swap and insert', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [9, 0, 1, 2, 3, 14, 15, 6, 7, 8].map(i => ({ index: i, contents: i }));
					await applyEditsAndVerify(cells);
				});
				test('Swap multiple and insert', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [1, 2, 3, 4, 6, 5, 9, 0].map(i => ({ index: i, contents: i }));
					await applyEditsAndVerify(cells);
				});
				test('Swap multiple and delete', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [1, 2, 3, 5, 6, 4, 0, 9].map(i => ({ index: i, contents: i }));
					await applyEditsAndVerify(cells);
				});
				test('Move top Cell to bottom', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0].map(i => ({ index: i, contents: i }));
					const { notebookEdits } = await applyEditsAndVerify(cells);

					expect(notebookEdits.length).toBe(2);
					expect(notebookEdits[0].range.start).toBe(0);
					expect(notebookEdits[1].range.start).toBe(9);
					expect(notebookEdits[1].newCells[0].value).toBe('0');
				});
				test('Insert 2 Cell at the top', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [10, 11, 0, 1, 2, 3, 4, 5, 6, 7, 8, 9].map(i => ({ index: i, contents: i }));
					const { notebookEdits } = await applyEditsAndVerify(cells);

					expect(notebookEdits.length).toBe(2);
					expect(notebookEdits[0].newCells[0].value).toBe('10');
					expect(notebookEdits[0].range.start).toBe(0);
					expect(notebookEdits[1].newCells[0].value).toBe('11');
					expect(notebookEdits[1].range.start).toBe(1);
				});
				test('Insert 8 Cell in the middle', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [0, 1, 2, 3, 4, 15, 16, 17, 18, 19, 20, 21, 22, 5, 6, 7, 8, 9].map(i => ({ index: i, contents: i }));
					const { notebookEdits } = await applyEditsAndVerify(cells);

					expect(notebookEdits.length).toBe(8);
				});
				test('Delete 3 cells from the middle', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [0, 1, 2, 3, 7, 8, 9].map(i => ({ index: i, contents: i }));
					const { notebookEdits } = await applyEditsAndVerify(cells);

					expect(notebookEdits.length).toBe(3);
				});
				test('Delete 3 Cell', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [{ index: 1, contents: 1 }, { index: 2, contents: 2 }, { index: 3, contents: 3 }, { index: 4, contents: 4 }, { index: 5, contents: 5 }, { index: 6, contents: 6 }, { index: 7, contents: 7 }];
					const { notebookEdits } = await applyEditsAndVerify(cells);

					// We should only have 3 deletes
					expect(notebookEdits.length).toBe(3);
					expect(notebookEdits[0].range.start).toBe(9);
					expect(notebookEdits[1].range.start).toBe(8);
					expect(notebookEdits[2].range.start).toBe(0);
				});
				test('Delete 3 Cell (from middle as well)', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [{ index: 1, contents: 1 }, { index: 2, contents: 2 }, { index: 3, contents: 3 }, { index: 4, contents: 4 }, { index: 6, contents: 6 }, { index: 7, contents: 7 }, { index: 8, contents: 8 }];
					const { notebookEdits } = await applyEditsAndVerify(cells);

					// We should only have 3 deletes
					expect(notebookEdits.length).toBe(3);
					expect(notebookEdits[0].range.start).toBe(9);
					expect(notebookEdits[1].range.start).toBe(5);
					expect(notebookEdits[2].range.start).toBe(0);
				});
				test('Delete first and update second', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [{ index: 1, contents: 2 }, { index: 2, contents: 2 }, { index: 3, contents: 3 }, { index: 4, contents: 4 }, { index: 6, contents: 6 }, { index: 7, contents: 7 }, { index: 8, contents: 8 }];
					await applyEditsAndVerify(cells);
				});
				test('Delete first, last and update few in middle', async () => {
					if (provider.kind !== 'xml') {
						return;
					}
					const cells = [{ index: 1, contents: 1 }, { index: 2, contents: 2222 }, { index: 3, contents: 999 }, { index: 4, contents: 4 }, { index: 6, contents: 6 }, { index: 7, contents: 7 }];
					await applyEditsAndVerify(cells);
				});
			});

			describe(`${provider.kind} Generate Edits instead of inserting and deleteing a cell (where id is missing)`, () => {
				test('Do not insert and delete the same cell if id is missing', async () => {
					const simulation = new SimulationWorkspace();
					const cells = [
						[`import sys`, `import os`],
						[`print(sys.executable)`]
					].map(contents => new NotebookCellData(NotebookCellKind.Code, contents.join(EOL), 'python'));
					const notebook = ExtHostNotebookDocumentData.fromNotebookData(Uri.file('test.ipynb'), new NotebookData(cells), 'jupyter-notebook', simulation).document;

					const newNotebook = ExtHostNotebookDocumentData.fromNotebookData(Uri.file('test2.ipynb'), new NotebookData(cells), 'jupyter-notebook', simulation).document;
					let alternativeContent = provider.getAlternativeDocument(newNotebook).getText();
					const id = summarize(newNotebook.getCells()[0]).id;
					alternativeContent = alternativeContent.replace(id, '');

					const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, textToAsyncIterableLines(alternativeContent), undefined, CancellationToken.None);
					const notebookEdits = await applyEditsSyncOrAsync(simulation, notebook, edits, applyEditsImmediately);

					expect(notebookEdits.length).toBe(0);
					notebook.getCells().forEach((cell, i) => {
						expect(cell.document.getText()).toBe(newNotebook.getCells()[i].document.getText());
					});
				});
				test('Do not insert and delete the same two cell if id is missing, just insert the new 3rd cell', async () => {
					const simulation = new SimulationWorkspace();
					let cells = [
						[`import sys`, `import os`],
						[`print(sys.executable)`],
					].map(contents => new NotebookCellData(NotebookCellKind.Code, contents.join(EOL), 'python'));
					const notebook = ExtHostNotebookDocumentData.fromNotebookData(Uri.file('test.ipynb'), new NotebookData(cells), 'jupyter-notebook', simulation).document;

					cells = [
						[`import sys`, `import os`],
						[`print(sys.executable)`],
						[`print("Hello World")`]
					].map(contents => new NotebookCellData(NotebookCellKind.Code, contents.join(EOL), 'python'));
					const newNotebook = ExtHostNotebookDocumentData.fromNotebookData(Uri.file('test2.ipynb'), new NotebookData(cells), 'jupyter-notebook', simulation).document;
					let alternativeContent = provider.getAlternativeDocument(newNotebook).getText();
					newNotebook.getCells().forEach(cell => {
						const id = summarize(cell).id;
						alternativeContent = alternativeContent.replace(id, '');
					});

					const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, textToAsyncIterableLines(alternativeContent), undefined, CancellationToken.None);
					const notebookEdits = await applyEditsSyncOrAsync(simulation, notebook, edits, applyEditsImmediately);

					expect(notebookEdits.length).toBe(1);
					expect(notebookEdits[0].range.start).toBe(2);
					expect(notebookEdits[0].newCells[0].value).toBe('print("Hello World")');
					expect(notebookEdits[0].newCells.length).toBe(1);
					notebook.getCells().forEach((cell, i) => {
						expect(cell.document.getText()).toBe(newNotebook.getCells()[i].document.getText());
					});
				});
				test('Insert new cell, instead of deleting the inserted cell', async () => {
					const simulation = new SimulationWorkspace();
					let cells = [
						[``],
					].map(contents => new NotebookCellData(NotebookCellKind.Code, contents.join(EOL), 'python'));
					const notebook = ExtHostNotebookDocumentData.fromNotebookData(Uri.file('test.ipynb'), new NotebookData(cells), 'jupyter-notebook', simulation).document;

					cells = [
						[`import sys`],
					].map(contents => new NotebookCellData(NotebookCellKind.Code, contents.join(EOL), 'python'));
					const newNotebook = ExtHostNotebookDocumentData.fromNotebookData(Uri.file('test2.ipynb'), new NotebookData(cells), 'jupyter-notebook', simulation).document;
					let alternativeContent = provider.getAlternativeDocument(newNotebook).getText();
					newNotebook.getCells().forEach(cell => {
						const id = summarize(cell).id;
						alternativeContent = alternativeContent.replace(id, '');
					});
					alternativeContent = alternativeContent.replace(`id=""`, '');
					const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, textToAsyncIterableLines(alternativeContent), undefined, CancellationToken.None);

					await applyEditsSyncOrAsync(simulation, notebook, edits, applyEditsImmediately);

					notebook.getCells().forEach((cell, i) => {
						expect(cell.document.getText()).toBe(newNotebook.getCells()[i].document.getText());
					});
				});
			});
		});
		describe('Malformed XML', () => {
			test('Missing line breaks in one cell', async () => {
				if (provider.kind !== 'xml') {
					return;
				}
				const simulation = new SimulationWorkspace();
				const cells = [
					[`import sys`],
					[`print(sys.executable)`],
					[`import os`],
					[`print(os.path)`],
				].map(contents => new NotebookCellData(NotebookCellKind.Code, contents.join(EOL), 'python'));
				const notebook = ExtHostNotebookDocumentData.fromNotebookData(Uri.file('test.ipynb'), new NotebookData(cells), 'jupyter-notebook', simulation).document;

				let alternativeContent = provider.getAlternativeDocument(notebook).getText();
				alternativeContent = alternativeContent.replace('sys.executable', '"Hello World"');
				alternativeContent = alternativeContent.split(/\r?\n/).join(EOL);
				// Remove the line break and ensure end cell tag is on the same line as the last line of code.
				alternativeContent = alternativeContent.replace(`print("Hello World")${EOL}</VSCode.Cell>`, `print("Hello World")</VSCode.Cell>`);

				const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, textToAsyncIterableLines(alternativeContent), undefined, CancellationToken.None);
				const notebookEdits = [];
				for await (const edit of edits) {
					if (Array.isArray(edit)) {
						simulation.applyEdits(edit[0], edit[1]);
					} else {
						simulation.applyNotebookEdits(notebook.uri, [edit]);
						notebookEdits.push(edit);
					}
				}

				expect(notebook.cellAt(0).document.getText()).toBe('import sys');
				expect(notebook.cellAt(1).document.getText()).toBe('print("Hello World")');
				expect(notebook.cellAt(2).document.getText()).toBe('import os');
				expect(notebook.cellAt(3).document.getText()).toBe('print(os.path)');
			});
			test('Missing line breaks in all cells', async () => {
				if (provider.kind !== 'xml') {
					return;
				}
				const simulation = new SimulationWorkspace();
				const cells = [
					[`import sys`],
					[`print(sys.executable)`],
					[`import os`],
					[`print(os.path)`],
				].map(contents => new NotebookCellData(NotebookCellKind.Code, contents.join(EOL), 'python'));
				const notebook = ExtHostNotebookDocumentData.fromNotebookData(Uri.file('test.ipynb'), new NotebookData(cells), 'jupyter-notebook', simulation).document;

				let alternativeContent = provider.getAlternativeDocument(notebook).getText();
				alternativeContent = alternativeContent.replace('sys.executable', '"Hello World"');
				alternativeContent = alternativeContent.split(/\r?\n/).join(EOL);
				// Remove the line break and ensure end cell tag is on the same line as the last line of code.
				alternativeContent = alternativeContent.replace(`${EOL}</VSCode.Cell>`, `</VSCode.Cell>`);

				const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, textToAsyncIterableLines(alternativeContent), undefined, CancellationToken.None);
				const notebookEdits = [];
				for await (const edit of edits) {
					if (Array.isArray(edit)) {
						simulation.applyEdits(edit[0], edit[1]);
					} else {
						simulation.applyNotebookEdits(notebook.uri, [edit]);
						notebookEdits.push(edit);
					}
				}

				expect(notebook.cellAt(0).document.getText()).toBe('import sys');
				expect(notebook.cellAt(1).document.getText()).toBe('print("Hello World")');
				expect(notebook.cellAt(2).document.getText()).toBe('import os');
				expect(notebook.cellAt(3).document.getText()).toBe('print(os.path)');
			});
			test('Deliberately include EndCell marker in a cell', async () => {
				if (provider.kind !== 'xml') {
					return;
				}
				const simulation = new SimulationWorkspace();
				const cells = [
					[`import sys`],
					[`print(sys.executable)`],
					[`import os</VSCode.Cell>`],
					[`print(os.path)`],
				].map(contents => new NotebookCellData(NotebookCellKind.Code, contents.join(EOL), 'python'));
				const notebook = ExtHostNotebookDocumentData.fromNotebookData(Uri.file('test.ipynb'), new NotebookData(cells), 'jupyter-notebook', simulation).document;

				let alternativeContent = provider.getAlternativeDocument(notebook).getText();
				alternativeContent = alternativeContent.replace('sys.executable', '"Hello World"');
				alternativeContent = alternativeContent.split(/\r?\n/).join(EOL);
				// Remove the line break and ensure end cell tag is on the same line as the last line of code.
				alternativeContent = alternativeContent.replace(`${EOL}</VSCode.Cell>`, `</VSCode.Cell>`);

				const edits = await getEditGenerator(provider).generateNotebookEdits(notebook, textToAsyncIterableLines(alternativeContent), undefined, CancellationToken.None);
				const notebookEdits = [];
				for await (const edit of edits) {
					if (Array.isArray(edit)) {
						simulation.applyEdits(edit[0], edit[1]);
					} else {
						simulation.applyNotebookEdits(notebook.uri, [edit]);
						notebookEdits.push(edit);
					}
				}

				expect(notebook.cellAt(0).document.getText()).toBe('import sys');
				expect(notebook.cellAt(1).document.getText()).toBe('print("Hello World")');
				expect(notebook.cellAt(2).document.getText()).toBe('import os</VSCode.Cell>');
				expect(notebook.cellAt(3).document.getText()).toBe('print(os.path)');
			});
		});
	});
});

function applyNotebookEdits(notebook: NotebookDocument, edits: (NotebookEdit | [Uri, TextEdit[]])[], simulationWorkspace: SimulationWorkspace) {
	const notebookEdits: NotebookEdit[] = [];
	for (const edit of edits) {
		if (Array.isArray(edit)) {
			simulationWorkspace.applyEdits(edit[0], edit[1]);
		} else {
			notebookEdits.push(edit);
		}
	}

	simulationWorkspace.applyNotebookEdits(notebook.uri, notebookEdits);
	return notebookDocumentToData(notebook);
}

function notebookDocumentToData(notebook: NotebookDocument): NotebookData {
	const newCells = notebook.getCells().map(notebookCellToCellData);
	const newCellMap = new ResourceMap<NotebookCellData>();
	notebook.getCells().forEach((cell, i) => {
		newCellMap.set(cell.document.uri, newCells[i]);
	});

	return new NotebookData(newCells);
}

function assertDocumentsAreEqual(notebook: NotebookDocument, data: NotebookData, kind: 'xml' | 'text' | 'json') {
	expect(notebook.cellCount).toBe(data.cells.length);
	for (let i = 0; i < notebook.cellCount; i++) {
		const cell = notebook.cellAt(i);
		const cellData = data.cells[i];
		// LLMs retun empty new lines for jupytext cells. Check the case of `reorder.ipynb`
		if (kind === 'text') {
			expect(normatlizeContent(cell.document.getText())).toBe(normatlizeContent(cellData.value));
		} else if (kind === 'json') {
			// With JSON with get extra padding and thats wrong.
			// E.g. doc string in python will have extra padding.
			// Before
			/**
			"source": [
				"import math",
				"",
				"def circle_area(radius):",
				"    print(\"HELLO WORLD\")",
				"    return math.pi * radius**2"
			]
			 */
			// Response from LLM, notice how the empty lines in docstrings are indented.
			/**
			"source": [
				"import math",
				"",
				"def circle_area(radius):",
				"    \"\"\"",
				"    Calculate the area of a circle given its radius.",
				"    ",
				"    Args:",
				"        radius (float): The radius of the circle.",
				"    ",
				"    Returns:",
				"        float: The area of the circle.",
				"    \"\"\"",
				"    print(\"HELLO WORLD\")",
				"    return math.pi * radius**2"
			]
			 */
			expect(normatlizeContent(cell.document.getText().split(/\r?\n/g).map(l => l.trim()).join('\n'))).toBe(normatlizeContent(cellData.value.split(/\r?\n/g).map(l => l.trim()).join('\n')));
		} else {
			expect(normatlizeContent(cell.document.getText())).toBe(normatlizeContent(cellData.value));
		}
		expect(cell.document.languageId).toBe(cellData.languageId);
		expect(cell.kind).toBe(cellData.kind);
	}
}


/**
 * Strip the id value from the string `id="2ce940c2"` to `id=""`.
 */
function normatlizeContent(content: string) {
	return content.
		replace(/id="[^"]+"/g, 'id=""'). // xml id
		replace(/id=[^"]+/g, 'id='). // jupytext id
		replace(/"id": "[^"]+"/g, '"id": ""'). // json id
		replace(/\r\n/g, '\n'). // windows/unix newlines
		trim();
}
