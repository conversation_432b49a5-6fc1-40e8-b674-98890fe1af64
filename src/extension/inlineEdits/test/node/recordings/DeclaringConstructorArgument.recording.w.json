{"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "5ac56762-f3b6-48a3-9e68-0afc136c3fae", "repoRootUri": "file:///c%3a/code", "opStart": 48559, "opEndEx": 49560}}, {"kind": "documentEncountered", "id": 0, "time": 1727780659937, "relativePath": "src\\platform\\inlineEdits\\common\\workspaceEditTracker\\nesWorkspaceEditTracker.ts"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1727780659937, "content": "/*---------------------------------------------------------------------------------------------\n *  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.\n *--------------------------------------------------------------------------------------------*/\n\nimport { findFirstMonotonous } from '../../../../util/vs/common/arraysFind';\nimport { OffsetRange } from '../../../../util/vs/editor/common/offsetRange';\nimport { DocumentUri } from '../dataTypes/documentUri';\nimport { Edit, RootedEdit } from '../dataTypes/edit';\nimport { StringValue } from '../dataTypes/stringValue';\nimport { TextEdit } from '../dataTypes/textEdit';\nimport { TextLengthEdit } from '../dataTypes/textEditLength';\nimport { Instant, now } from '../utils/utils';\nimport { IWorkspaceEditTracker, RecentWorkspaceEdits } from './workspaceEditTracker';\n\nexport class NesWorkspaceEditTracker implements IWorkspaceEditTracker {\n\tprivate readonly _documentState = new Map<DocumentUri, DocumentState>();\n\n\tpublic handleDocumentOpened(docUri: DocumentUri, state: StringValue): void {\n\t\tthis._documentState.set(docUri, new DocumentState(state.value));\n\t}\n\n\tpublic handleEdit(docUri: DocumentUri, edit: Edit): void {\n\t\tconst state = this._documentState.get(docUri)!;\n\t\tstate.handleEdit(edit);\n\t}\n\n\tpublic handleDocumentClosed(docUri: DocumentUri): void {\n\t\tthis._documentState.delete(docUri);\n\t}\n\n\tpublic getRecentEdit(docUri: DocumentUri): RecentWorkspaceEdits | undefined {\n\t\tconst state = this._documentState.get(docUri);\n\t\tif (!state) { return undefined; }\n\t\treturn state.getRecentEdit();\n\t}\n}\n\nclass DocumentState {\n\tprivate baseValue: StringValue;\n\tprivate currentValue: StringValue;\n\tprivate edits: { edit: Edit; textLengthEdit: TextLengthEdit; instant: Instant }[] = [];\n\n\tconstructor(initialValue: string) {\n\t\tthis.baseValue = new StringValue(initialValue);\n\t\tthis.currentValue = this.baseValue;\n\t}\n\n\thandleEdit(edit: Edit): void {\n\t\tif (edit.isEmpty()) { return; }\n\t\tthis.currentValue = this.currentValue.apply(edit);\n\t\tconst textEdit = TextEdit.fromEdit(edit, this.currentValue);\n\t\tconst textLengthEdit = TextLengthEdit.fromTextEdit(textEdit);\n\n\t\tconst lastEdit = this.edits.at(-1);\n\t\tif (lastEdit && editExtends(edit, lastEdit.edit)) {\n\t\t\tlastEdit.edit = lastEdit.edit.compose(edit);\n\t\t\tlastEdit.textLengthEdit = lastEdit.textLengthEdit.compose(textLengthEdit);\n\t\t\tlastEdit.instant = now();\n\t\t\tif (lastEdit.edit.isEmpty()) {\n\t\t\t\tthis.edits.pop();\n\t\t\t}\n\t\t} else {\n\t\t\tthis.edits.push({ edit, textLengthEdit, instant: now() });\n\t\t}\n\t}\n\n\tgetRecentEdit(): RecentWorkspaceEdits | undefined {\n\t\tthis._applyStaleEdits();\n\n\t\tif (this.edits.length === 0) { return undefined; }\n\n\t\tconst composedEdits = Edit.compose(this.edits.map(e => e.edit));\n\t\tconst lastEdit = this.edits.at(-1)!.edit;\n\t\tconst recentEditRange = composedEdits.applyInverseToOffsetRange(lastEdit.getNewRanges()[0]);\n\n\t\tconst result = new RootedEdit(this.baseValue, composedEdits);\n\t\treturn new RecentWorkspaceEdits(result, recentEditRange!);\n\t}\n\n\tprivate _applyStaleEdits(): void {\n\t\tlet recentEdit = Edit.empty;\n\t\tlet i: number;\n\t\tlet count = 0;\n\t\tfor (i = this.edits.length - 1; i >= 0 && count < 5; i--, count++) {\n\t\t\tconst e = this.edits[i];\n\n\t\t\tif (now() - e.instant > 10 * 60 * 1000) { break; }\n\n\t\t\tconst changedLines = sum(e.textLengthEdit.edits, e => (e.range.endLineNumber - e.range.startLineNumber) + e.newLength.lineCount);\n\t\t\tif (changedLines > 10) { break; }\n\n\t\t\t// We take the edit!\n\t\t\trecentEdit = e.edit.compose(recentEdit);\n\t\t}\n\n\t\t// remove & apply the edits we didn't take\n\t\tfor (let j = 0; j <= i; j++) {\n\t\t\tconst e = this.edits[j];\n\t\t\tthis.baseValue = this.baseValue.apply(e.edit);\n\t\t}\n\n\t\tthis.edits = this.edits.slice(i + 1);\n\t}\n}\n\nfunction sum<T>(arr: readonly T[], f: (t: T) => number): number {\n\tlet result = 0;\n\tfor (const e of arr) {\n\t\tresult += f(e);\n\t}\n\treturn result;\n}\n\nfunction editExtends(edit: Edit, previousEdit: Edit): boolean {\n\tconst newRanges = previousEdit.getNewRanges();\n\treturn edit.edits.every(e => intersectsOrTouches(e.range, newRanges));\n}\n\nfunction intersectsOrTouches(range: OffsetRange, sortedRanges: readonly OffsetRange[]): boolean {\n\tconst firstCandidate = findFirstMonotonous(sortedRanges, r => r.endExclusive >= range.start);\n\treturn firstCandidate ? firstCandidate.intersectsOrTouches(range) : false;\n}\n"}, {"kind": "changed", "id": 0, "time": 1727780077920, "edit": [[1570, 1570, "\nclass \n"]]}, {"kind": "changed", "id": 0, "time": 1727780082122, "edit": [[1577, 1577, "FifoQueue {\n\t\n}"]]}, {"kind": "changed", "id": 0, "time": 1727780112748, "edit": [[1586, 1586, "<T>"], [1589, 1590, "\t"]]}, {"kind": "changed", "id": 0, "time": 1727780124467, "edit": []}, {"kind": "changed", "id": 0, "time": 1727780126963, "edit": []}, {"kind": "changed", "id": 0, "time": 1727780131305, "edit": [[1593, 1593, "constructor(\n\t\tpublic readonly size: number\n\t)"]]}, {"kind": "changed", "id": 0, "time": 1727780132903, "edit": [[1639, 1639, " {\n\t\t\n\t}"]]}, {"kind": "changed", "id": 0, "time": 1727780135041, "edit": [[1641, 1644, ""]]}, {"kind": "changed", "id": 0, "time": 1727780135697, "edit": [[1644, 1644, "\n\n\t"]]}, {"kind": "changed", "id": 0, "time": 1727780141655, "edit": [[1624, 1625, "maxS"], [1646, 1647, ""]]}, {"kind": "changed", "id": 0, "time": 1727780142488, "edit": [[1648, 1648, "\t"]]}, {"kind": "changed", "id": 0, "time": 1727780144183, "edit": [[1591, 1591, "\n\t"], [1648, 1649, ""]]}, {"kind": "changed", "id": 0, "time": 1727780146538, "edit": [[1593, 1593, "private "]]}, {"kind": "changed", "id": 0, "time": 1727780151711, "edit": [[1592, 1601, "\tprivate _arr: T[] = [];"]]}, {"kind": "changed", "id": 0, "time": 1727780152800, "edit": [[1616, 1616, "\n\t"]]}, {"kind": "changed", "id": 0, "time": 1727780154188, "edit": [[1617, 1618, ""], [1676, 1676, "\t"]]}, {"kind": "changed", "id": 0, "time": 1727780158844, "edit": [[1675, 1676, "\tpush(e: T): void {\n\t\tthis._arr.push(e);\n\t\tif (this._arr.length > this.maxSize) {\n\t\t\tthis._arr.shift();\n\t\t}\n\t}"]]}, {"kind": "changed", "id": 0, "time": 1727780172976, "edit": [[979, 979, "\tprivate readonly _lastDocuments\n"]]}, {"kind": "changed", "id": 0, "time": 1727780178325, "edit": [[979, 1011, "\tprivate readonly _lastDocuments = new FifoQueue<DocumentUri>(5);"]]}, {"kind": "changed", "id": 0, "time": 1727780184227, "edit": [[1304, 1304, "\n\t\tthis._lastDocuments."]]}, {"kind": "changed", "id": 0, "time": 1727780185629, "edit": [[1326, 1327, ".push()"]]}, {"kind": "changed", "id": 0, "time": 1727780190484, "edit": [[1028, 1039, "DocumentState"]]}, {"kind": "changed", "id": 0, "time": 1727780216297, "edit": [[1022, 1024, "S"], [1025, 1027, "t"], [1678, 1680, "S"], [1681, 1683, "t"]]}, {"kind": "changed", "id": 0, "time": 1727780224032, "edit": [[1790, 1875, ""]]}, {"kind": "changed", "id": 0, "time": 1727780225591, "edit": [[1709, 1709, "\n\t"]]}, {"kind": "changed", "id": 0, "time": 1727780228016, "edit": [[1711, 1711, "private _set"]]}, {"kind": "changed", "id": 0, "time": 1727780229140, "edit": [[1710, 1723, "\tprivate _set = new Set<T>();"]]}, {"kind": "changed", "id": 0, "time": 1727780231370, "edit": [[1709, 1739, ""]]}, {"kind": "changed", "id": 0, "time": 1727780232739, "edit": [[1788, 1790, "\t\t"]]}, {"kind": "changed", "id": 0, "time": 1727780239220, "edit": [[1790, 1790, "if "]]}, {"kind": "changed", "id": 0, "time": 1727780242956, "edit": [[1793, 1793, "(this._arr.)"]]}, {"kind": "changed", "id": 0, "time": 1727780246514, "edit": [[1790, 1794, "const existing = "]]}, {"kind": "changed", "id": 0, "time": 1727780248153, "edit": [[1807, 1818, "find"]]}, {"kind": "changed", "id": 0, "time": 1727780252160, "edit": [[1807, 1811, "this._arr"]]}, {"kind": "changed", "id": 0, "time": 1727780256187, "edit": [[1788, 1816, "\t\tconst existing = this._arr.indexOf(e);\n\t\t"]]}, {"kind": "changed", "id": 0, "time": 1727780271365, "edit": [[1829, 1831, "\t\tif (existing !== -1) {\n\t\t\tthis._arr.splice(existing, 1);\n\t\t} else if (this._arr.length >= this.maxSize) {\n\t\t\tthis._arr.shift();\n\t\t}"]]}, {"kind": "changed", "id": 0, "time": 1727780273718, "edit": [[1887, 1887, "\n\t\t\t"]]}, {"kind": "changed", "id": 0, "time": 1727780275086, "edit": [[1888, 1891, "\t\t\tthis._arr.push(e);"]]}, {"kind": "changed", "id": 0, "time": 1727780280799, "edit": [[1888, 1909, ""]]}, {"kind": "changed", "id": 0, "time": 1727780281881, "edit": [[1963, 1963, "\n\t\tthis._arr.push(e);"]]}, {"kind": "changed", "id": 0, "time": 1727780282307, "edit": [[1887, 1888, ""]]}, {"kind": "changed", "id": 0, "time": 1727780298421, "edit": [[1332, 1332, "state"]]}, {"kind": "changed", "id": 0, "time": 1727780298779, "edit": [[1338, 1338, ";"]]}, {"kind": "changed", "id": 0, "time": 1727780441658, "edit": [[2957, 2957, "editCount: number"]]}, {"kind": "changed", "id": 0, "time": 1727780443143, "edit": [[3036, 3036, "editCount"]]}, {"kind": "changed", "id": 0, "time": 1727780455248, "edit": [[2977, 2977, "{ edits: "]]}, {"kind": "changed", "id": 0, "time": 1727780459907, "edit": [[3006, 3006, ", editCount: number }"]]}, {"kind": "changed", "id": 0, "time": 1727780461443, "edit": [[3006, 3007, ";"]]}, {"kind": "changed", "id": 0, "time": 1727780466904, "edit": [[3495, 3495, "editCount: number"]]}, {"kind": "changed", "id": 0, "time": 1727780471136, "edit": [[3639, 3640, "editCount"]]}, {"kind": "changed", "id": 0, "time": 1727780633141, "edit": [[3412, 3413, " { edits: "]]}, {"kind": "changed", "id": 0, "time": 1727780633888, "edit": [[3472, 3472, " }"]]}, {"kind": "changed", "id": 0, "time": 1727780636110, "edit": [[3414, 3415, "\n\t\t\t"]]}, {"kind": "changed", "id": 0, "time": 1727780637190, "edit": [[3475, 3476, "\n\t\t"]]}, {"kind": "changed", "id": 0, "time": 1727780642670, "edit": [[3475, 3475, ",\n\t\t\teditCount: this.edits.length,"]]}, {"kind": "changed", "id": 0, "time": 1727780654833, "edit": [[3429, 3449, "RecentDocumentEdit"]]}, {"kind": "changed", "id": 0, "time": 1727780654893, "edit": [[778, 778, "RecentDocumentEdit, "]]}, {"kind": "changed", "id": 0, "time": 1727780657555, "edit": [[3468, 3468, "this. "]]}, {"kind": "changed", "id": 0, "time": 1727780659937, "edit": [[3473, 3473, "<PERSON><PERSON><PERSON><PERSON>,"]]}], "nextUserEdit": {"edit": [[1196, 1196, "<PERSON><PERSON><PERSON><PERSON>, "], [1653, 1653, "\n\t\t"], [2210, 2210, "\n\t\tpublic readonly docUri: DocumentUri,\n\t\t"], [2230, 2230, "\n\t"], [3006, 3026, "RecentDocumentEdit"]], "relativePath": "src\\platform\\inlineEdits\\common\\workspaceEditTracker\\nesWorkspaceEditTracker.ts", "originalOpIdx": 49717}}