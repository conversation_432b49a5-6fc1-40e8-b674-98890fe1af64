{
	// Use IntelliSense to learn about possible attributes.
	// Hover to view descriptions of existing attributes.
	// For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
	"version": "0.2.0",
	"configurations": [
		{
			"args": [
				"--extensionDevelopmentPath=${workspaceFolder}/test-extension"
			],
			"name": "Launch Simulation Test Runner",
			"outFiles": [
				"${workspaceFolder}/.vscode/extensions/test-extension/dist/**/*.js"
			],
			// "preLaunchTask": "npm",
			"request": "launch",
			"type": "extensionHost"
		}
	]
}
