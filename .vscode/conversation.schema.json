{"$schema": "http://json-schema.org/draft-07/schema", "type": "array", "definitions": {"keyword-list": {"type": "array", "items": {"type": ["string", "object"], "anyOf": [{"type": "string"}, {"$ref": "#/definitions/keyword-object"}]}}, "keyword-object": {"type": "object", "properties": {"allOf": {"description": "A list of keywords or keyword lists that must all be satisfied in the response for the test to pass.", "$ref": "#/definitions/keyword-list"}, "anyOf": {"description": "A list of keywords or keyword lists which at least one must be satisfied in the response for the test to pass.", "$ref": "#/definitions/keyword-list"}, "not": {"description": "A list of keywords or keyword lists that must not be satisfied in the response for the test to pass.", "$ref": "#/definitions/keyword-list"}}}, "keywords": {"anyOf": [{"type": "array", "$ref": "#/definitions/keyword-list"}, {"type": "object", "$ref": "#/definitions/keyword-object"}]}}, "items": {"type": "object", "required": ["question"], "properties": {"question": {"description": "The user question to send to Copilot.", "type": "string"}, "stateFile": {"description": "The relative path to the state file to use for this question.", "type": "string"}, "keywords": {"description": "The keywords to expect or exclude from the response.", "$ref": "#/definitions/keywords"}}}, "additionalProperties": false}