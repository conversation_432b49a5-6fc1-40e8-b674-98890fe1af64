.testListMenu {
	position: absolute;
	z-index: 1000;
	border: 1px solid black;
}

.testListMenu .hidden {
	display: none;
}

.clickable {
	cursor: pointer;
}

.testRun {
	cursor: initial;
	padding-left: 20px;
	padding-bottom: 30px;
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	gap: 10px;
}

.foldingBar {
	width: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: rgb(228, 227, 227);
}

.foldingBar:hover {
	background-color: rgb(216, 216, 216);
}

.content {
	width: 100%;
}

.request-container .title {
	cursor: pointer;
}

.toolbar {
	padding: 5px;
}

.toolbar .dropdown {
	display: inline-block;
	margin-right: 10px;
}

.toolbar input {
	margin-left: 5px;
}

.toolbar input.nruns {
	width: 20px;
}

.toolbar button.button {
	margin-left: 5px;
}

.toolbar div.useCache {
	display: inline-block;
	vertical-align: middle;
}

.go-to-mode-button {
	float: right;
	font-size: 13px !important;
}

.external-toolbar-filter {
	display: inline;
	margin-left: 10px;
	margin-right: 10px;
}

.external-toolbar-filter .external-toolbar-dropdown {
	margin-left: 10px;
	margin-right: 10px;
}

.tests-renderer {
	font-family: "Helvetica Neue Light", "HelveticaNeue-Light", "Helvetica Neue", "Segoe UI", Calibri, Helvetica, Arial, sans-serif;
	font-size: 12px;
}

.test-renderer {
	margin-bottom: 1px;
	padding-top: 2px;
	padding-bottom: 2px;
}

.test-renderer .runner-status {
	float: left;
	width: 15px;
	height: 15px;
}

.test-renderer .runner-status.running::before {
	content: '🏃';
}

.test-renderer .runner-status.running {
	background-color: #5e740b;
}

.test-renderer .runner-status.pending::before {
	content: '⌛';
}

.test-renderer .runner-status.pending {
	background-color: #d2e0e6;
}

.test-renderer .runner-status.finished::before {
	content: '🏁';
}

.test-renderer .runner-status.cancelled::before {
	content: '⏹️';
}

.test-renderer .runner-status.skipped::before {
	content: '⏭️';
}

.test-renderer .runner-status.not-run::before {
	content: '🔘';
}

.test-renderer .test-score {
	float: left;
	padding-right: 5px;
	text-align: right;
	border-right: #000 2px;
	font-weight: bold;
	white-space: pre;
	font-family: monospace;
}

.test-renderer .suite-title {
	float: left;
	font-weight: bold;
	margin-right: 4px;
}

.test-renderer .test-title {
	float: left;
	font-weight: bold;
}

.test-renderer .test-title .test-duration {
	font-size: 80%;
	opacity: 0.8;
}

.test-runs-container {
	border-top: 2px solid black;
}

.test-renderer .test-run-renderer.fail {
	background-color: #f2dede;
}

.request-container {
	margin-top: 5px;
	margin-bottom: 10px;
}

.request-container .title {
	font-family: Arial, "Segoe UI";
	font-size: 1em;
	font-weight: bold;
}

.request-container .reply .monaco-editor-background,
.request-container .reply .monaco-editor,
.request-container .reply .monaco-editor .margin {
	background-color: rgb(218, 255, 234);
}

.file-editor-container {
	margin-top: 10px;
	margin-left: 20px;
	width: 95%;
	border: none;
	border-radius: 2pt;
	box-shadow: 0 0 0 2pt #0000004b;
}

.file-editor-draggable-border {
	height: 5px;
	width: 95%;
	background: #ccc;
	cursor: row-resize;
	margin-left: 20px;
}

.file-editor-draggable-border:hover {
	background: #4d90fe;
}

.step-title {
	font-family: Arial, "Segoe UI";
	font-size: 1.1em;
	font-weight: bold;
	margin-top: 5px;
}

.error-comparison {
	font-family: Arial, 'Segoe UI';
	margin-left: 30px;
	margin-top: 10px;
	margin-bottom: 10px;
}

.error-comparison .title {
	font-size: 1em;
	font-weight: bold;
}

.error-comparison .category {
	font-weight: bold;
}

.diagnostics-comparison {
	font-size: 1em;
}

.diagnostics-comparison {
	margin-left: 30px;
}

.step-query {
	font-family: Monaco, 'Cascadia Code', monospace;
	white-space: pre;
	word-break: break-word;
	text-wrap: wrap;
}

.assertion-error {
	margin-left: 20px;
	padding-bottom: 10px;
}

.monaco-editor .step-range-highlight {
	background-color: rgba(235, 219, 0, 0.2);
}

.monaco-editor .dec-diagnostic {
	outline: 1px solid red;
}

.monaco-editor .dec-diagnostic-invalid-range {
	outline: 2px solid red;
	background-color: #f004;
}

.monaco-editor .cursor {
	visibility: inherit !important;
}

/*
.step-markdown {
	font-family: Arial;
	width: 400px;
	margin-left: 30px
}
.step-markdown code {
	font-family: Monaco;
	color: red;
} */

.scorecard-container {
	margin-top: 10px;
	margin-bottom: 10px;
}

.scorecard-title {
	font-size: 1.25em;
}

.scorecard-table {
	border: 1px solid black;
	border-collapse: collapse;
}

.scorecard-table td,
.scorecard-table th {
	border: 1px solid black;
	padding: 4px;
}

.tooltip .tooltiptext {
	visibility: hidden;
	width: auto;
	background-color: #555;
	color: #fff;
	text-align: center;
	border-radius: 3px;
	padding: 3px 5px;
	position: absolute;
	z-index: 1;
	opacity: 0;
	transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
	visibility: visible;
	opacity: 1;
}

.tooltip .tooltiptext.top {
	bottom: 125%;
	left: 50%;
	margin-left: -60px;
}

.tooltip .tooltiptext.bottom {
	top: 125%;
	left: 50%;
	margin-left: -60px;
}

.tooltip .tooltiptext.left {
	right: 50%;
	margin-right: -60px;
}

.tooltip .tooltiptext.right {
	left: 50%;
	margin-left: -60px;
}

.context-menu {
	position: fixed;
	z-index: 1000;
	background-color: #fff;
	border: 1px solid #ccc;
	box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
	padding: 5px 0;
	min-width: 150px;
	display: none;
}

.fade-out-background {
    animation: fadeOutBackground 5s ease-out forwards;
}

@keyframes fadeOutBackground {
    from { background-color: lightgreen; }
    to { background-color: transparent; }
}
