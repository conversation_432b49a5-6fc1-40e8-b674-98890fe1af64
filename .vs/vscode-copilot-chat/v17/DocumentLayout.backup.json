{"Version": 1, "WorkspaceRootPath": "C:\\github\\vscode-copilot-chat\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccess.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\conversation\\vscode-node\\languageModelAccess.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\platform\\endpoint\\node\\autoChatEndpoint.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\platform\\endpoint\\node\\autoChatEndpoint.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentInstructions.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompts\\node\\agent\\agentInstructions.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\intents\\node\\agentIntent.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\intents\\node\\agentIntent.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\panel\\customInstructions.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompts\\node\\panel\\customInstructions.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompts\\node\\agent\\agentPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\.github\\copilot-instructions.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:.github\\copilot-instructions.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\package.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:package.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicProvider.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\byok\\vscode-node\\anthropicProvider.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\intents\\node\\toolCallingLoop.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\intents\\node\\toolCallingLoop.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\vscode-copilot-chat\\src\\extension\\intents\\node\\editCodeIntent.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\intents\\node\\editCodeIntent.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Document", "DocumentIndex": 12, "Title": "anthropicProvider.ts", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicProvider.ts", "RelativeDocumentMoniker": "src\\extension\\byok\\vscode-node\\anthropicProvider.ts", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicProvider.ts", "RelativeToolTip": "src\\extension\\byok\\vscode-node\\anthropicProvider.ts", "ViewState": "AgIAAAcAAAAAAAAAAAAAABUAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T15:41:46.845Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 13, "Title": "anthropicMessageConverter.ts", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts", "RelativeDocumentMoniker": "src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts", "RelativeToolTip": "src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts", "ViewState": "AgIAAFwAAAAAAAAAAAApwG0AAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T15:42:37.374Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 7, "Title": "customInstructions.tsx", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\panel\\customInstructions.tsx", "RelativeDocumentMoniker": "src\\extension\\prompts\\node\\panel\\customInstructions.tsx", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\panel\\customInstructions.tsx", "RelativeToolTip": "src\\extension\\prompts\\node\\panel\\customInstructions.tsx", "ViewState": "AgIAABMAAAAAAAAAAAAowCAAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-10T21:19:11.581Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "extChatEndpoint.ts", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts", "RelativeDocumentMoniker": "src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts", "RelativeToolTip": "src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts", "ViewState": "AgIAAH4AAAAAAAAAAAAowIUAAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T17:04:05.834Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "languageModelAccessPrompt.tsx", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx", "RelativeDocumentMoniker": "src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx", "RelativeToolTip": "src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx", "ViewState": "AgIAAAAAAAAAAAAAAAAAACEAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T17:14:49.029Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "pullRequestDescriptionPrompt.tsx", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx", "RelativeDocumentMoniker": "src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx", "RelativeToolTip": "src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx", "ViewState": "AgIAAFkAAAAAAAAAAIAwwG4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T17:17:25.136Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "languageModelAccess.ts", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccess.ts", "RelativeDocumentMoniker": "src\\extension\\conversation\\vscode-node\\languageModelAccess.ts", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccess.ts", "RelativeToolTip": "src\\extension\\conversation\\vscode-node\\languageModelAccess.ts", "ViewState": "AgIAALQBAAAAAAAAAAAMwMMBAAA4AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T17:13:11.036Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "agentIntent.ts", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\intents\\node\\agentIntent.ts", "RelativeDocumentMoniker": "src\\extension\\intents\\node\\agentIntent.ts", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\intents\\node\\agentIntent.ts", "RelativeToolTip": "src\\extension\\intents\\node\\agentIntent.ts", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-10T23:11:05.376Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "agentInstructions.tsx", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentInstructions.tsx", "RelativeDocumentMoniker": "src\\extension\\prompts\\node\\agent\\agentInstructions.tsx", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentInstructions.tsx", "RelativeToolTip": "src\\extension\\prompts\\node\\agent\\agentInstructions.tsx", "ViewState": "AgIAABcAAAAAAAAAAAAkwCMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-10T21:20:51.069Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "autoChatEndpoint.ts", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\platform\\endpoint\\node\\autoChatEndpoint.ts", "RelativeDocumentMoniker": "src\\platform\\endpoint\\node\\autoChatEndpoint.ts", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\platform\\endpoint\\node\\autoChatEndpoint.ts", "RelativeToolTip": "src\\platform\\endpoint\\node\\autoChatEndpoint.ts", "ViewState": "AgIAAC8AAAAAAAAAAAD4v0QAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T15:29:01.947Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "agentPrompt.tsx", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentPrompt.tsx", "RelativeDocumentMoniker": "src\\extension\\prompts\\node\\agent\\agentPrompt.tsx", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentPrompt.tsx", "RelativeToolTip": "src\\extension\\prompts\\node\\agent\\agentPrompt.tsx", "ViewState": "AgIAAFUAAAAAAAAAAAD8v2MAAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-10T21:17:42.11Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "defaultIntentRequestHandler.ts", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts", "RelativeDocumentMoniker": "src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts", "RelativeToolTip": "src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts", "ViewState": "AgIAACQBAAAAAAAAAAAYwDkBAAAzAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T16:35:33.662Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "copilot-instructions.md", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\.github\\copilot-instructions.md", "RelativeDocumentMoniker": ".github\\copilot-instructions.md", "ToolTip": "C:\\github\\vscode-copilot-chat\\.github\\copilot-instructions.md", "RelativeToolTip": ".github\\copilot-instructions.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-07T16:29:48.453Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "package.json", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\package.json", "RelativeDocumentMoniker": "package.json", "ToolTip": "C:\\github\\vscode-copilot-chat\\package.json", "RelativeToolTip": "package.json", "ViewState": "AgIAAHsJAAAAAAAAAAAkwI8JAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-07T16:29:11.103Z"}, {"$type": "Document", "DocumentIndex": 15, "Title": "editCodeIntent.ts", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\intents\\node\\editCodeIntent.ts", "RelativeDocumentMoniker": "src\\extension\\intents\\node\\editCodeIntent.ts", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\intents\\node\\editCodeIntent.ts", "RelativeToolTip": "src\\extension\\intents\\node\\editCodeIntent.ts", "ViewState": "AgIAAGwAAAAAAAAAAAAdwHMAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T15:38:08.122Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "toolCallingLoop.ts", "DocumentMoniker": "C:\\github\\vscode-copilot-chat\\src\\extension\\intents\\node\\toolCallingLoop.ts", "RelativeDocumentMoniker": "src\\extension\\intents\\node\\toolCallingLoop.ts", "ToolTip": "C:\\github\\vscode-copilot-chat\\src\\extension\\intents\\node\\toolCallingLoop.ts", "RelativeToolTip": "src\\extension\\intents\\node\\toolCallingLoop.ts", "ViewState": "AgIAAJ0BAAAAAAAAAIAqwJUBAAAnAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T15:36:12.81Z"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}]}]}]}