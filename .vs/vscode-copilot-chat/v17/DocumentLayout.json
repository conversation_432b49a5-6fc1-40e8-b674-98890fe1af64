{"Version": 1, "WorkspaceRootPath": "C:\\github\\deep-agent\\vscode-copilot-chat\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompts\\node\\agent\\agentPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\onboardDebug\\vscode-node\\copilotDebugCommandContribution.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\onboardDebug\\vscode-node\\copilotDebugCommandContribution.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\linkify\\common\\filePathLinkifier.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\linkify\\common\\filePathLinkifier.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\configuration\\vscode-node\\configurationMigration.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\configuration\\vscode-node\\configurationMigration.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\node\\readFileTool.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\tools\\node\\readFileTool.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\common\\toolsRegistry.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\tools\\common\\toolsRegistry.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\common\\toolNames.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\tools\\common\\toolNames.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompt\\node\\chatParticipantRequestHandler.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompt\\node\\chatParticipantRequestHandler.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\package.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:package.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\test\\scenarios\\test-tools\\tools.0.conversation.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:test\\scenarios\\test-tools\\tools.0.conversation.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\node\\test\\testFailure.spec.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\tools\\node\\test\\testFailure.spec.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\test\\scenarios\\test-tools\\tools.1.conversation.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:test\\scenarios\\test-tools\\tools.1.conversation.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompt\\common\\conversation.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompt\\common\\conversation.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\intents\\node\\editCodeIntent.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\intents\\node\\editCodeIntent.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\intents\\node\\toolCallingLoop.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\intents\\node\\toolCallingLoop.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\test\\languageModelAccess.test.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\conversation\\vscode-node\\test\\languageModelAccess.test.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccess.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\conversation\\vscode-node\\languageModelAccess.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\platform\\endpoint\\node\\autoChatEndpoint.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\platform\\endpoint\\node\\autoChatEndpoint.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\byok\\node\\openAIEndpoint.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\byok\\node\\openAIEndpoint.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\codeMapper\\codeMapper.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompts\\node\\codeMapper\\codeMapper.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\remoteAgents.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\conversation\\vscode-node\\remoteAgents.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentInstructions.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompts\\node\\agent\\agentInstructions.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\intents\\node\\agentIntent.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\intents\\node\\agentIntent.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\panel\\customInstructions.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\prompts\\node\\panel\\customInstructions.tsx||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\.github\\copilot-instructions.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:.github\\copilot-instructions.md||{EFC0BB08-EA7D-40C6-A696-C870411A895B}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicProvider.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\byok\\vscode-node\\anthropicProvider.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts||{0F2454B1-A556-402D-A7D0-1FDE7F99DEE0}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 6, "Children": [{"$type": "Document", "DocumentIndex": 29, "Title": "anthropicProvider.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicProvider.ts", "RelativeDocumentMoniker": "src\\extension\\byok\\vscode-node\\anthropicProvider.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicProvider.ts", "RelativeToolTip": "src\\extension\\byok\\vscode-node\\anthropicProvider.ts", "ViewState": "AgIAAAcAAAAAAAAAAAAAABUAAAAiAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T15:41:46.845Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 30, "Title": "anthropicMessageConverter.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts", "RelativeDocumentMoniker": "src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts", "RelativeToolTip": "src\\extension\\byok\\vscode-node\\anthropicMessageConverter.ts", "ViewState": "AgIAAFwAAAAAAAAAAAApwG0AAAAvAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T15:42:37.374Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 27, "Title": "customInstructions.tsx", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\panel\\customInstructions.tsx", "RelativeDocumentMoniker": "src\\extension\\prompts\\node\\panel\\customInstructions.tsx", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\panel\\customInstructions.tsx", "RelativeToolTip": "src\\extension\\prompts\\node\\panel\\customInstructions.tsx", "ViewState": "AgIAABMAAAAAAAAAAAAowCAAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-10T21:19:11.581Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 8, "Title": "extChatEndpoint.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts", "RelativeDocumentMoniker": "src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts", "RelativeToolTip": "src\\platform\\endpoint\\vscode-node\\extChatEndpoint.ts", "ViewState": "AgIAALAAAAAAAAAAAAAMwMUAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T17:04:05.834Z", "IsPinned": true, "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "languageModelAccessPrompt.tsx", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx", "RelativeDocumentMoniker": "src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx", "RelativeToolTip": "src\\extension\\conversation\\vscode-node\\languageModelAccessPrompt.tsx", "ViewState": "AgIAAAAAAAAAAAAAAAAAACEAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T17:14:49.029Z", "IsPinned": true}, {"$type": "Document", "DocumentIndex": 1, "Title": "copilotDebugCommandContribution.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\onboardDebug\\vscode-node\\copilotDebugCommandContribution.ts", "RelativeDocumentMoniker": "src\\extension\\onboardDebug\\vscode-node\\copilotDebugCommandContribution.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\onboardDebug\\vscode-node\\copilotDebugCommandContribution.ts", "RelativeToolTip": "src\\extension\\onboardDebug\\vscode-node\\copilotDebugCommandContribution.ts", "ViewState": "AgIAAC4AAAAAAAAAAAAMwEQAAAAaAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-12T00:44:45.596Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "agentPrompt.tsx", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentPrompt.tsx", "RelativeDocumentMoniker": "src\\extension\\prompts\\node\\agent\\agentPrompt.tsx", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentPrompt.tsx", "RelativeToolTip": "src\\extension\\prompts\\node\\agent\\agentPrompt.tsx", "ViewState": "AgIAABMCAAAAAAAAAAAMwCkCAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-10T21:17:42.11Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "filePathLinkifier.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\linkify\\common\\filePathLinkifier.ts", "RelativeDocumentMoniker": "src\\extension\\linkify\\common\\filePathLinkifier.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\linkify\\common\\filePathLinkifier.ts", "RelativeToolTip": "src\\extension\\linkify\\common\\filePathLinkifier.ts", "ViewState": "AgIAAGwAAAAAAAAAAAAMwIIAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-12T00:45:26.305Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "configurationMigration.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\configuration\\vscode-node\\configurationMigration.ts", "RelativeDocumentMoniker": "src\\extension\\configuration\\vscode-node\\configurationMigration.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\configuration\\vscode-node\\configurationMigration.ts", "RelativeToolTip": "src\\extension\\configuration\\vscode-node\\configurationMigration.ts", "ViewState": "AgIAADwAAAAAAAAAAAD4v1EAAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-12T00:45:06.152Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "readFileTool.tsx", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\node\\readFileTool.tsx", "RelativeDocumentMoniker": "src\\extension\\tools\\node\\readFileTool.tsx", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\node\\readFileTool.tsx", "RelativeToolTip": "src\\extension\\tools\\node\\readFileTool.tsx", "ViewState": "AgIAAJ4AAAAAAAAAAAAMwLcAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-12T00:03:49.897Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "toolsRegistry.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\common\\toolsRegistry.ts", "RelativeDocumentMoniker": "src\\extension\\tools\\common\\toolsRegistry.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\common\\toolsRegistry.ts", "RelativeToolTip": "src\\extension\\tools\\common\\toolsRegistry.ts", "ViewState": "AgIAACIAAAAAAAAAAAAMwC0AAAA7AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-12T00:04:41.121Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "toolNames.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\common\\toolNames.ts", "RelativeDocumentMoniker": "src\\extension\\tools\\common\\toolNames.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\common\\toolNames.ts", "RelativeToolTip": "src\\extension\\tools\\common\\toolNames.ts", "ViewState": "AgIAAIkAAAAAAAAAAAAtwJwAAABGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T23:39:03.76Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "chatParticipantRequestHandler.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompt\\node\\chatParticipantRequestHandler.ts", "RelativeDocumentMoniker": "src\\extension\\prompt\\node\\chatParticipantRequestHandler.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompt\\node\\chatParticipantRequestHandler.ts", "RelativeToolTip": "src\\extension\\prompt\\node\\chatParticipantRequestHandler.ts", "ViewState": "AgIAAKoAAAAAAAAAAAAMwMAAAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T18:38:59.394Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "package.json", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\package.json", "RelativeDocumentMoniker": "package.json", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\package.json", "RelativeToolTip": "package.json", "ViewState": "AgIAAKMAAAAAAAAAAAAmwK0AAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-07T16:29:11.103Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "tools.0.conversation.json", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\test\\scenarios\\test-tools\\tools.0.conversation.json", "RelativeDocumentMoniker": "test\\scenarios\\test-tools\\tools.0.conversation.json", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\test\\scenarios\\test-tools\\tools.0.conversation.json", "RelativeToolTip": "test\\scenarios\\test-tools\\tools.0.conversation.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABEAAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-11T23:39:55.845Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "testFailure.spec.tsx", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\node\\test\\testFailure.spec.tsx", "RelativeDocumentMoniker": "src\\extension\\tools\\node\\test\\testFailure.spec.tsx", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\tools\\node\\test\\testFailure.spec.tsx", "RelativeToolTip": "src\\extension\\tools\\node\\test\\testFailure.spec.tsx", "ViewState": "AgIAAPMAAAAAAAAAAAAMwAkBAABdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T23:41:02.801Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "tools.1.conversation.json", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\test\\scenarios\\test-tools\\tools.1.conversation.json", "RelativeDocumentMoniker": "test\\scenarios\\test-tools\\tools.1.conversation.json", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\test\\scenarios\\test-tools\\tools.1.conversation.json", "RelativeToolTip": "test\\scenarios\\test-tools\\tools.1.conversation.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABQAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-11T23:41:14.682Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "conversation.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompt\\common\\conversation.ts", "RelativeDocumentMoniker": "src\\extension\\prompt\\common\\conversation.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompt\\common\\conversation.ts", "RelativeToolTip": "src\\extension\\prompt\\common\\conversation.ts", "ViewState": "AgIAACIBAAAAAAAAAAD4vzcBAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T18:38:47.334Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "toolCallingLoop.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\intents\\node\\toolCallingLoop.ts", "RelativeDocumentMoniker": "src\\extension\\intents\\node\\toolCallingLoop.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\intents\\node\\toolCallingLoop.ts", "RelativeToolTip": "src\\extension\\intents\\node\\toolCallingLoop.ts", "ViewState": "AgIAADwBAAAAAAAAAAAowFIBAABeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T15:36:12.81Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "editCodeIntent.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\intents\\node\\editCodeIntent.ts", "RelativeDocumentMoniker": "src\\extension\\intents\\node\\editCodeIntent.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\intents\\node\\editCodeIntent.ts", "RelativeToolTip": "src\\extension\\intents\\node\\editCodeIntent.ts", "ViewState": "AgIAAGIAAAAAAAAAAAAowH0AAAAjAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T15:38:08.122Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "defaultIntentRequestHandler.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts", "RelativeDocumentMoniker": "src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts", "RelativeToolTip": "src\\extension\\prompt\\node\\defaultIntentRequestHandler.ts", "ViewState": "AgIAACgBAAAAAAAAAAACwEEBAAAeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T16:35:33.662Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "languageModelAccess.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccess.ts", "RelativeDocumentMoniker": "src\\extension\\conversation\\vscode-node\\languageModelAccess.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\languageModelAccess.ts", "RelativeToolTip": "src\\extension\\conversation\\vscode-node\\languageModelAccess.ts", "ViewState": "AgIAACEBAAAAAAAAAAAuwC4BAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T17:13:11.036Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "languageModelAccess.test.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\test\\languageModelAccess.test.ts", "RelativeDocumentMoniker": "src\\extension\\conversation\\vscode-node\\test\\languageModelAccess.test.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\test\\languageModelAccess.test.ts", "RelativeToolTip": "src\\extension\\conversation\\vscode-node\\test\\languageModelAccess.test.ts", "ViewState": "AgIAADoAAAAAAAAAAAAuwEcAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T18:17:39.649Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "autoChatEndpoint.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\platform\\endpoint\\node\\autoChatEndpoint.ts", "RelativeDocumentMoniker": "src\\platform\\endpoint\\node\\autoChatEndpoint.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\platform\\endpoint\\node\\autoChatEndpoint.ts", "RelativeToolTip": "src\\platform\\endpoint\\node\\autoChatEndpoint.ts", "ViewState": "AgIAADwAAAAAAAAAAAAuwEkAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-07T15:29:01.947Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "openAIEndpoint.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\byok\\node\\openAIEndpoint.ts", "RelativeDocumentMoniker": "src\\extension\\byok\\node\\openAIEndpoint.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\byok\\node\\openAIEndpoint.ts", "RelativeToolTip": "src\\extension\\byok\\node\\openAIEndpoint.ts", "ViewState": "AgIAAHEAAAAAAAAAAAAQwH0AAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T18:16:15.099Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "codeMapper.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\codeMapper\\codeMapper.ts", "RelativeDocumentMoniker": "src\\extension\\prompts\\node\\codeMapper\\codeMapper.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\codeMapper\\codeMapper.ts", "RelativeToolTip": "src\\extension\\prompts\\node\\codeMapper\\codeMapper.ts", "ViewState": "AgIAAIkCAAAAAAAAAAAQwJUCAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T18:16:12.5Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "remoteAgents.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\remoteAgents.ts", "RelativeDocumentMoniker": "src\\extension\\conversation\\vscode-node\\remoteAgents.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\conversation\\vscode-node\\remoteAgents.ts", "RelativeToolTip": "src\\extension\\conversation\\vscode-node\\remoteAgents.ts", "ViewState": "AgIAAIwBAAAAAAAAAAAQwJgBAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T18:16:08.553Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "pullRequestDescriptionPrompt.tsx", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx", "RelativeDocumentMoniker": "src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx", "RelativeToolTip": "src\\extension\\prompts\\node\\github\\pullRequestDescriptionPrompt.tsx", "ViewState": "AgIAAFkAAAAAAAAAAIAwwG4AAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-11T17:17:25.136Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "agentIntent.ts", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\intents\\node\\agentIntent.ts", "RelativeDocumentMoniker": "src\\extension\\intents\\node\\agentIntent.ts", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\intents\\node\\agentIntent.ts", "RelativeToolTip": "src\\extension\\intents\\node\\agentIntent.ts", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAABBAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-10T23:11:05.376Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "agentInstructions.tsx", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentInstructions.tsx", "RelativeDocumentMoniker": "src\\extension\\prompts\\node\\agent\\agentInstructions.tsx", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\src\\extension\\prompts\\node\\agent\\agentInstructions.tsx", "RelativeToolTip": "src\\extension\\prompts\\node\\agent\\agentInstructions.tsx", "ViewState": "AgIAABcAAAAAAAAAAAAkwCMAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003213|", "WhenOpened": "2025-07-10T21:20:51.069Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "copilot-instructions.md", "DocumentMoniker": "C:\\github\\deep-agent\\vscode-copilot-chat\\.github\\copilot-instructions.md", "RelativeDocumentMoniker": ".github\\copilot-instructions.md", "ToolTip": "C:\\github\\deep-agent\\vscode-copilot-chat\\.github\\copilot-instructions.md", "RelativeToolTip": ".github\\copilot-instructions.md", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001818|", "WhenOpened": "2025-07-07T16:29:48.453Z"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}]}]}]}